<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;

/* modules/custom/client_demandes/templates/client-dashboard.html.twig */
class __TwigTemplate_cb29ed3587124ef5e1941b083fb8ac66 extends Template
{
    private $source;
    private $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->parent = false;

        $this->blocks = [
        ];
        $this->sandbox = $this->env->getExtension('\Twig\Extension\SandboxExtension');
        $this->checkSecurity();
    }

    protected function doDisplay(array $context, array $blocks = [])
    {
        $macros = $this->macros;
        // line 4
        echo "
";
        // line 5
        echo $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, $this->extensions['Drupal\Core\Template\TwigExtension']->attachLibrary("client_demandes/dashboard"), "html", null, true);
        echo "
";
        // line 6
        echo $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, $this->extensions['Drupal\Core\Template\TwigExtension']->attachLibrary("client_demandes/chat"), "html", null, true);
        echo "
";
        // line 7
        echo $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, $this->extensions['Drupal\Core\Template\TwigExtension']->attachLibrary("bootstrap5/bootstrap5-js-latest"), "html", null, true);
        echo "

<div class=\"client-dashboard container py-4\">
  <div class=\"d-flex justify-content-between align-items-center mb-4\">
    <div>
      <h1>
        <i class=\"fas fa-tachometer-alt me-2\"></i>
        ";
        // line 14
        echo $this->extensions['Drupal\Core\Template\TwigExtension']->renderVar(t("Dashboard Client"));
        echo "
      </h1>
      <p class=\"text-muted\">";
        // line 16
        echo $this->extensions['Drupal\Core\Template\TwigExtension']->renderVar(t("Gérez vos demandes et suivez vos devis"));
        echo "</p>
    </div>
    <div class=\"btn-group\">
      <a href=\"";
        // line 19
        echo $this->extensions['Drupal\Core\Template\TwigExtension']->renderVar($this->extensions['Drupal\Core\Template\TwigExtension']->getPath("client_demandes.nouvelle_demande"));
        echo "\" class=\"btn btn-success\">
        <i class=\"fas fa-plus me-2\"></i>
        ";
        // line 21
        echo $this->extensions['Drupal\Core\Template\TwigExtension']->renderVar(t("Faire une demande"));
        echo "
      </a>
      <a href=\"/client/mes-demandes\" class=\"btn btn-outline-secondary\">
        <i class=\"fas fa-list me-2\"></i>
        ";
        // line 25
        echo $this->extensions['Drupal\Core\Template\TwigExtension']->renderVar(t("Mes demandes"));
        echo "
      </a>
      <a href=\"";
        // line 27
        echo $this->extensions['Drupal\Core\Template\TwigExtension']->renderVar($this->extensions['Drupal\Core\Template\TwigExtension']->getPath("client_demandes.client_commerce_orders"));
        echo "\" class=\"btn btn-outline-info\">
        <i class=\"fas fa-shopping-cart me-2\"></i>
        ";
        // line 29
        echo $this->extensions['Drupal\Core\Template\TwigExtension']->renderVar(t("Mes commandes"));
        echo "
      </a>
    </div>
  </div>

  ";
        // line 35
        echo "  ";
        if (($context["statistiques"] ?? null)) {
            // line 36
            echo "    <div class=\"row g-4 mb-5\">
      <div class=\"col-md-6\">
        <div class=\"card shadow-sm\">
          <div class=\"card-header bg-primary text-white\">
            <h5 class=\"mb-0\">
              <i class=\"fas fa-chart-line me-2\"></i>
              ";
            // line 42
            echo $this->extensions['Drupal\Core\Template\TwigExtension']->renderVar(t("Activité"));
            echo "
            </h5>
          </div>
          <div class=\"card-body\">
            <div class=\"d-flex justify-content-between mb-3\">
              <span>";
            // line 47
            echo $this->extensions['Drupal\Core\Template\TwigExtension']->renderVar(t("Demandes créées"));
            echo "</span>
              <strong>";
            // line 48
            echo $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, $this->sandbox->ensureToStringAllowed(twig_get_attribute($this->env, $this->source, ($context["statistiques"] ?? null), "total_demandes", [], "any", false, false, true, 48), 48, $this->source), "html", null, true);
            echo "</strong>
            </div>
            <div class=\"d-flex justify-content-between\">
              <span>";
            // line 51
            echo $this->extensions['Drupal\Core\Template\TwigExtension']->renderVar(t("Devis reçus"));
            echo "</span>
              <strong>";
            // line 52
            echo $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, $this->sandbox->ensureToStringAllowed(twig_get_attribute($this->env, $this->source, ($context["statistiques"] ?? null), "devis_recus", [], "any", false, false, true, 52), 52, $this->source), "html", null, true);
            echo "</strong>
            </div>
          </div>
        </div>
      </div>
      <div class=\"col-md-6\">
        <div class=\"card shadow-sm\">
          <div class=\"card-header bg-success text-white\">
            <h5 class=\"mb-0\">
              <i class=\"fas fa-euro-sign me-2\"></i>
              ";
            // line 62
            echo $this->extensions['Drupal\Core\Template\TwigExtension']->renderVar(t("Finances"));
            echo "
            </h5>
          </div>
          <div class=\"card-body\">
            <div class=\"d-flex justify-content-between mb-3\">
              <span>";
            // line 67
            echo $this->extensions['Drupal\Core\Template\TwigExtension']->renderVar(t("Projets payés"));
            echo "</span>
              <strong>";
            // line 68
            echo $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, $this->sandbox->ensureToStringAllowed(twig_get_attribute($this->env, $this->source, ($context["statistiques"] ?? null), "devis_payes", [], "any", false, false, true, 68), 68, $this->source), "html", null, true);
            echo "</strong>
            </div>
            <div class=\"d-flex justify-content-between mb-3\">
              <span>";
            // line 71
            echo $this->extensions['Drupal\Core\Template\TwigExtension']->renderVar(t("Total dépensé"));
            echo "</span>
              <strong>";
            // line 72
            echo $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, twig_number_format_filter($this->env, $this->sandbox->ensureToStringAllowed(twig_get_attribute($this->env, $this->source, ($context["statistiques"] ?? null), "montant_total_depense", [], "any", false, false, true, 72), 72, $this->source), 0, ",", " "), "html", null, true);
            echo "€</strong>
            </div>
            <div class=\"d-flex justify-content-between\">
              <span>";
            // line 75
            echo $this->extensions['Drupal\Core\Template\TwigExtension']->renderVar(t("Dépôts effectués"));
            echo "</span>
              <strong class=\"text-warning\">
                <i class=\"fas fa-piggy-bank me-1\"></i>
                ";
            // line 78
            echo $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, twig_number_format_filter($this->env, $this->sandbox->ensureToStringAllowed(twig_get_attribute($this->env, $this->source, ($context["statistiques"] ?? null), "montant_depots", [], "any", false, false, true, 78), 78, $this->source), 0, ",", " "), "html", null, true);
            echo "€
              </strong>
            </div>
          </div>
        </div>
      </div>
    </div>
  ";
        }
        // line 86
        echo "
  ";
        // line 88
        echo "  <div class=\"row g-4\">
    ";
        // line 90
        echo "    <div class=\"col-lg-4\">
      <div class=\"card shadow-sm h-100\">
        <div class=\"card-header bg-primary text-white d-flex justify-content-between align-items-center\">
          <h5 class=\"mb-0\">
            <i class=\"fas fa-comments me-2\"></i>
            ";
        // line 95
        echo $this->extensions['Drupal\Core\Template\TwigExtension']->renderVar(t("Messages"));
        echo "
          </h5>
          <button class=\"btn btn-outline-light btn-sm chat-history-toggle-btn\" title=\"";
        // line 97
        echo $this->extensions['Drupal\Core\Template\TwigExtension']->renderVar(t("Voir l'historique des conversations"));
        echo "\">
            <i class=\"fas fa-history me-1\"></i>
            ";
        // line 99
        echo $this->extensions['Drupal\Core\Template\TwigExtension']->renderVar(t("Historique"));
        echo "
          </button>
        </div>
        <div class=\"card-body p-0\">
          <div class=\"chat-container-embedded\">
            <div class=\"conversations-list-embedded\">
              <!-- Les conversations seront chargées ici via AJAX -->
            </div>
            <div class=\"chat-window-embedded\">
              <div class=\"chat-window-header-embedded\">
                ";
        // line 109
        echo $this->extensions['Drupal\Core\Template\TwigExtension']->renderVar(t("Sélectionnez une conversation"));
        echo "
              </div>
              <div class=\"messages-list-embedded\">
                <div class=\"no-messages text-center py-4\">
                  <i class=\"fas fa-comments fa-2x text-muted mb-2\"></i>
                  <p class=\"text-muted\">";
        // line 114
        echo $this->extensions['Drupal\Core\Template\TwigExtension']->renderVar(t("Aucune conversation sélectionnée"));
        echo "</p>
                </div>
              </div>
              <div class=\"chat-message-form-embedded\" style=\"display: none;\">
                <form class=\"d-flex\">
                  <input type=\"text\" class=\"form-control chat-message-input\" placeholder=\"";
        // line 119
        echo $this->extensions['Drupal\Core\Template\TwigExtension']->renderVar(t("Tapez votre message..."));
        echo "\">
                  <button type=\"submit\" class=\"btn btn-primary chat-send-button\">
                    <i class=\"fas fa-paper-plane\"></i>
                  </button>
                </form>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    ";
        // line 132
        echo "    <div class=\"col-lg-8\">
      <div class=\"card shadow-sm\">
        <div class=\"card-header bg-info text-white\">
          <h5 class=\"mb-0\">
            <i class=\"fas fa-file-invoice me-2\"></i>
            ";
        // line 137
        echo $this->extensions['Drupal\Core\Template\TwigExtension']->renderVar(t("Devis reçus - Liste"));
        echo "
            ";
        // line 138
        if (($context["devis_list"] ?? null)) {
            // line 139
            echo "              <span class=\"badge bg-light text-dark ms-2\">";
            echo $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, twig_length_filter($this->env, $this->sandbox->ensureToStringAllowed(($context["devis_list"] ?? null), 139, $this->source)), "html", null, true);
            echo "</span>
            ";
        }
        // line 141
        echo "          </h5>
        </div>
        <div class=\"card-body\">
          ";
        // line 144
        if ((($context["devis_list"] ?? null) && (twig_length_filter($this->env, ($context["devis_list"] ?? null)) > 0))) {
            // line 145
            echo "            <div class=\"table-responsive\">
              <table class=\"table table-hover\">
                <thead class=\"table-dark\">
                  <tr>
                    <th>";
            // line 149
            echo $this->extensions['Drupal\Core\Template\TwigExtension']->renderVar(t("Prestataire"));
            echo "</th>
                    <th>";
            // line 150
            echo $this->extensions['Drupal\Core\Template\TwigExtension']->renderVar(t("Prix"));
            echo "</th>
                    <th>";
            // line 151
            echo $this->extensions['Drupal\Core\Template\TwigExtension']->renderVar(t("Date"));
            echo "</th>
                    <th>";
            // line 152
            echo $this->extensions['Drupal\Core\Template\TwigExtension']->renderVar(t("Statut"));
            echo "</th>
                    <th>";
            // line 153
            echo $this->extensions['Drupal\Core\Template\TwigExtension']->renderVar(t("Actions"));
            echo "</th>
                  </tr>
                </thead>
                <tbody>
                  ";
            // line 157
            $context['_parent'] = $context;
            $context['_seq'] = twig_ensure_traversable(($context["devis_list"] ?? null));
            foreach ($context['_seq'] as $context["_key"] => $context["devis"]) {
                // line 158
                echo "                    <tr>
                      <td>
                        <strong>";
                // line 160
                echo $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, ((twig_get_attribute($this->env, $this->source, $context["devis"], "prestataire_name", [], "any", true, true, true, 160)) ? (_twig_default_filter($this->sandbox->ensureToStringAllowed(twig_get_attribute($this->env, $this->source, $context["devis"], "prestataire_name", [], "any", false, false, true, 160), 160, $this->source), "Prestataire")) : ("Prestataire")), "html", null, true);
                echo "</strong>
                        <br>
                        <small class=\"text-muted\">";
                // line 162
                echo $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, ((twig_get_attribute($this->env, $this->source, $context["devis"], "demande_title", [], "any", true, true, true, 162)) ? (_twig_default_filter($this->sandbox->ensureToStringAllowed(twig_get_attribute($this->env, $this->source, $context["devis"], "demande_title", [], "any", false, false, true, 162), 162, $this->source), "Demande")) : ("Demande")), "html", null, true);
                echo "</small>
                      </td>
                      <td>
                        <strong>";
                // line 165
                echo $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, twig_number_format_filter($this->env, ((twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, $context["devis"], "field_price", [], "any", false, true, true, 165), "value", [], "any", true, true, true, 165)) ? (_twig_default_filter($this->sandbox->ensureToStringAllowed(twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, $context["devis"], "field_price", [], "any", false, true, true, 165), "value", [], "any", false, false, true, 165), 165, $this->source), 0)) : (0)), 0, ",", " "), "html", null, true);
                echo "€</strong>
                        ";
                // line 166
                if ((twig_get_attribute($this->env, $this->source, $context["devis"], "reste_a_payer", [], "any", true, true, true, 166) && (twig_get_attribute($this->env, $this->source, $context["devis"], "reste_a_payer", [], "any", false, false, true, 166) > 0))) {
                    // line 167
                    echo "                          <br>
                          <small class=\"text-warning\">
                            <i class=\"fas fa-exclamation-triangle me-1\"></i>
                            ";
                    // line 170
                    echo $this->extensions['Drupal\Core\Template\TwigExtension']->renderVar(t("Reste: "));
                    echo $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, twig_number_format_filter($this->env, $this->sandbox->ensureToStringAllowed(twig_get_attribute($this->env, $this->source, $context["devis"], "reste_a_payer", [], "any", false, false, true, 170), 170, $this->source), 0, ",", " "), "html", null, true);
                    echo "€
                          </small>
                        ";
                }
                // line 173
                echo "                      </td>
                      <td>
                        <small>";
                // line 175
                echo $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, twig_date_format_filter($this->env, $this->sandbox->ensureToStringAllowed(twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, $context["devis"], "created", [], "any", false, false, true, 175), "value", [], "any", false, false, true, 175), 175, $this->source), "d/m/Y"), "html", null, true);
                echo "</small>
                      </td>
                      <td>
                        ";
                // line 178
                if ((twig_get_attribute($this->env, $this->source, $context["devis"], "field_status", [], "any", false, false, true, 178) && twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, $context["devis"], "field_status", [], "any", false, false, true, 178), "value", [], "any", false, false, true, 178))) {
                    // line 179
                    echo "                          ";
                    $context["status"] = twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, $context["devis"], "field_status", [], "any", false, false, true, 179), "value", [], "any", false, false, true, 179);
                    // line 180
                    echo "                          ";
                    if ((($context["status"] ?? null) == "pending")) {
                        // line 181
                        echo "                            <span class=\"badge bg-warning\">";
                        echo $this->extensions['Drupal\Core\Template\TwigExtension']->renderVar(t("En attente"));
                        echo "</span>
                          ";
                    } elseif ((                    // line 182
($context["status"] ?? null) == "accepted")) {
                        // line 183
                        echo "                            <span class=\"badge bg-success\">";
                        echo $this->extensions['Drupal\Core\Template\TwigExtension']->renderVar(t("Accepté"));
                        echo "</span>
                          ";
                    } elseif ((                    // line 184
($context["status"] ?? null) == "paid")) {
                        // line 185
                        echo "                            <span class=\"badge bg-primary\">";
                        echo $this->extensions['Drupal\Core\Template\TwigExtension']->renderVar(t("Payé"));
                        echo "</span>
                          ";
                    } elseif ((                    // line 186
($context["status"] ?? null) == "partial_paid")) {
                        // line 187
                        echo "                            <span class=\"badge bg-info\">";
                        echo $this->extensions['Drupal\Core\Template\TwigExtension']->renderVar(t("Partiellement payé"));
                        echo "</span>
                          ";
                    } elseif ((                    // line 188
($context["status"] ?? null) == "completed")) {
                        // line 189
                        echo "                            <span class=\"badge bg-success\">";
                        echo $this->extensions['Drupal\Core\Template\TwigExtension']->renderVar(t("Terminé"));
                        echo "</span>
                          ";
                    } elseif ((                    // line 190
($context["status"] ?? null) == "ready_to_close")) {
                        // line 191
                        echo "                            <span class=\"badge bg-warning\">";
                        echo $this->extensions['Drupal\Core\Template\TwigExtension']->renderVar(t("Prêt à clôturer"));
                        echo "</span>
                          ";
                    } elseif ((                    // line 192
($context["status"] ?? null) == "closed")) {
                        // line 193
                        echo "                            <span class=\"badge bg-dark\">";
                        echo $this->extensions['Drupal\Core\Template\TwigExtension']->renderVar(t("Clôturé"));
                        echo "</span>
                          ";
                    } elseif ((                    // line 194
($context["status"] ?? null) == "refused")) {
                        // line 195
                        echo "                            <span class=\"badge bg-danger\">";
                        echo $this->extensions['Drupal\Core\Template\TwigExtension']->renderVar(t("Refusé"));
                        echo "</span>
                          ";
                    }
                    // line 197
                    echo "                        ";
                } else {
                    // line 198
                    echo "                          <span class=\"badge bg-secondary\">";
                    echo $this->extensions['Drupal\Core\Template\TwigExtension']->renderVar(t("Inconnu"));
                    echo "</span>
                        ";
                }
                // line 200
                echo "                      </td>
                      <td>
                        <div class=\"btn-group-sm\">
                          ";
                // line 203
                if ((twig_get_attribute($this->env, $this->source, $context["devis"], "field_status", [], "any", false, false, true, 203) && twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, $context["devis"], "field_status", [], "any", false, false, true, 203), "value", [], "any", false, false, true, 203))) {
                    // line 204
                    echo "                            ";
                    $context["status"] = twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, $context["devis"], "field_status", [], "any", false, false, true, 204), "value", [], "any", false, false, true, 204);
                    // line 205
                    echo "                            ";
                    if ((($context["status"] ?? null) == "pending")) {
                        // line 206
                        echo "                              <a href=\"/client/devis/";
                        echo $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, $this->sandbox->ensureToStringAllowed(twig_get_attribute($this->env, $this->source, $context["devis"], "id", [], "any", false, false, true, 206), 206, $this->source), "html", null, true);
                        echo "/accepter\" class=\"btn btn-success btn-sm\">
                                <i class=\"fas fa-check me-1\"></i>";
                        // line 207
                        echo $this->extensions['Drupal\Core\Template\TwigExtension']->renderVar(t("Accepter"));
                        echo "
                              </a>
                              <a href=\"/client/devis/";
                        // line 209
                        echo $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, $this->sandbox->ensureToStringAllowed(twig_get_attribute($this->env, $this->source, $context["devis"], "id", [], "any", false, false, true, 209), 209, $this->source), "html", null, true);
                        echo "/refuser\" class=\"btn btn-danger btn-sm\">
                                <i class=\"fas fa-times me-1\"></i>";
                        // line 210
                        echo $this->extensions['Drupal\Core\Template\TwigExtension']->renderVar(t("Refuser"));
                        echo "
                              </a>
                            ";
                    } elseif ((                    // line 212
($context["status"] ?? null) == "accepted")) {
                        // line 213
                        echo "                              <a href=\"/client/paiement-options/";
                        echo $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, $this->sandbox->ensureToStringAllowed(twig_get_attribute($this->env, $this->source, $context["devis"], "id", [], "any", false, false, true, 213), 213, $this->source), "html", null, true);
                        echo "\" class=\"btn btn-primary btn-sm\">
                                <i class=\"fas fa-credit-card me-1\"></i>";
                        // line 214
                        echo $this->extensions['Drupal\Core\Template\TwigExtension']->renderVar(t("Payer"));
                        echo "
                              </a>
                            ";
                    } elseif ((((                    // line 216
($context["status"] ?? null) == "paid") || (($context["status"] ?? null) == "depot")) || (($context["status"] ?? null) == "completed"))) {
                        // line 217
                        echo "                              <button class=\"btn btn-warning btn-sm btn-cloture-projet\" data-devis-id=\"";
                        echo $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, $this->sandbox->ensureToStringAllowed(twig_get_attribute($this->env, $this->source, $context["devis"], "id", [], "any", false, false, true, 217), 217, $this->source), "html", null, true);
                        echo "\" data-csrf-token=\"";
                        echo $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, $this->sandbox->ensureToStringAllowed(($context["csrf_token_cloture"] ?? null), 217, $this->source), "html", null, true);
                        echo "\">
                                <i class=\"fas fa-flag-checkered me-1\"></i>";
                        // line 218
                        echo $this->extensions['Drupal\Core\Template\TwigExtension']->renderVar(t("Clôturer le projet"));
                        echo "
                              </button>
                              <button class=\"btn btn-info btn-sm btn-cloture-avec-code\" data-devis-id=\"";
                        // line 220
                        echo $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, $this->sandbox->ensureToStringAllowed(twig_get_attribute($this->env, $this->source, $context["devis"], "id", [], "any", false, false, true, 220), 220, $this->source), "html", null, true);
                        echo "\" data-csrf-token=\"";
                        echo $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, $this->sandbox->ensureToStringAllowed(($context["csrf_token_cloture"] ?? null), 220, $this->source), "html", null, true);
                        echo "\">
                                <i class=\"fas fa-key me-1\"></i>";
                        // line 221
                        echo $this->extensions['Drupal\Core\Template\TwigExtension']->renderVar(t("Clôturer avec code"));
                        echo "
                              </button>
                            ";
                    } elseif ((                    // line 223
($context["status"] ?? null) == "ready_to_close")) {
                        // line 224
                        echo "                              <span class=\"text-muted small\">";
                        echo $this->extensions['Drupal\Core\Template\TwigExtension']->renderVar(t("En attente de validation prestataire"));
                        echo "</span>
                            ";
                    } elseif ((                    // line 225
($context["status"] ?? null) == "closed")) {
                        // line 226
                        echo "                              <button class=\"btn btn-success btn-sm btn-liberer-fonds\" data-devis-id=\"";
                        echo $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, $this->sandbox->ensureToStringAllowed(twig_get_attribute($this->env, $this->source, $context["devis"], "id", [], "any", false, false, true, 226), 226, $this->source), "html", null, true);
                        echo "\" data-csrf-token=\"";
                        echo $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, $this->sandbox->ensureToStringAllowed(($context["csrf_token_cloture"] ?? null), 226, $this->source), "html", null, true);
                        echo "\">
                                <i class=\"fas fa-money-bill-wave me-1\"></i>";
                        // line 227
                        echo $this->extensions['Drupal\Core\Template\TwigExtension']->renderVar(t("Libérer les fonds"));
                        echo "
                              </button>
                            ";
                    }
                    // line 230
                    echo "                          ";
                }
                // line 231
                echo "                          ";
                if ((((twig_get_attribute($this->env, $this->source, $context["devis"], "field_demande_ref", [], "any", false, false, true, 231) && twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, $context["devis"], "field_demande_ref", [], "any", false, false, true, 231), "target_id", [], "any", false, false, true, 231)) && twig_get_attribute($this->env, $this->source, $context["devis"], "uid", [], "any", false, false, true, 231)) && twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, $context["devis"], "uid", [], "any", false, false, true, 231), "target_id", [], "any", false, false, true, 231))) {
                    // line 232
                    echo "                            <button class=\"btn btn-outline-primary btn-sm\" onclick=\"openChatFromButton(";
                    echo $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, $this->sandbox->ensureToStringAllowed(twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, $context["devis"], "field_demande_ref", [], "any", false, false, true, 232), "target_id", [], "any", false, false, true, 232), 232, $this->source), "html", null, true);
                    echo ", ";
                    echo $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, $this->sandbox->ensureToStringAllowed(twig_get_attribute($this->env, $this->source, twig_get_attribute($this->env, $this->source, $context["devis"], "uid", [], "any", false, false, true, 232), "target_id", [], "any", false, false, true, 232), 232, $this->source), "html", null, true);
                    echo ", '";
                    echo $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, ((twig_get_attribute($this->env, $this->source, $context["devis"], "prestataire_name", [], "any", true, true, true, 232)) ? (_twig_default_filter($this->sandbox->ensureToStringAllowed(twig_get_attribute($this->env, $this->source, $context["devis"], "prestataire_name", [], "any", false, false, true, 232), 232, $this->source), "Prestataire")) : ("Prestataire")), "html", null, true);
                    echo "', '";
                    echo $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, ((twig_get_attribute($this->env, $this->source, $context["devis"], "demande_title", [], "any", true, true, true, 232)) ? (_twig_default_filter($this->sandbox->ensureToStringAllowed(twig_get_attribute($this->env, $this->source, $context["devis"], "demande_title", [], "any", false, false, true, 232), 232, $this->source), "Demande")) : ("Demande")), "html", null, true);
                    echo "', 'prestataire')\">
                              <i class=\"fas fa-comments me-1\"></i>";
                    // line 233
                    echo $this->extensions['Drupal\Core\Template\TwigExtension']->renderVar(t("Chat"));
                    echo "
                            </button>
                          ";
                }
                // line 236
                echo "                        </div>
                      </td>
                    </tr>
                  ";
            }
            $_parent = $context['_parent'];
            unset($context['_seq'], $context['_iterated'], $context['_key'], $context['devis'], $context['_parent'], $context['loop']);
            $context = array_intersect_key($context, $_parent) + $_parent;
            // line 240
            echo "                </tbody>
              </table>
            </div>
          ";
        } else {
            // line 244
            echo "            <div class=\"text-center py-5\">
              <i class=\"fas fa-inbox fa-3x text-muted mb-3\"></i>
              <h5 class=\"text-muted\">";
            // line 246
            echo $this->extensions['Drupal\Core\Template\TwigExtension']->renderVar(t("Aucun devis reçu"));
            echo "</h5>
              <p class=\"text-muted\">";
            // line 247
            echo $this->extensions['Drupal\Core\Template\TwigExtension']->renderVar(t("Créez une demande pour recevoir des devis de prestataires"));
            echo "</p>
              <a href=\"";
            // line 248
            echo $this->extensions['Drupal\Core\Template\TwigExtension']->renderVar($this->extensions['Drupal\Core\Template\TwigExtension']->getPath("client_demandes.nouvelle_demande"));
            echo "\" class=\"btn btn-success\">
                <i class=\"fas fa-plus me-2\"></i>";
            // line 249
            echo $this->extensions['Drupal\Core\Template\TwigExtension']->renderVar(t("Créer une demande"));
            echo "
              </a>
            </div>
          ";
        }
        // line 253
        echo "        </div>
      </div>
    </div>
  </div>
</div>

";
        // line 260
        echo "<div class=\"modal fade\" id=\"modalClotureAvecCode\" tabindex=\"-1\" aria-labelledby=\"modalClotureAvecCodeLabel\" aria-hidden=\"true\">
  <div class=\"modal-dialog\">
    <div class=\"modal-content\">
      <div class=\"modal-header\">
        <h5 class=\"modal-title\" id=\"modalClotureAvecCodeLabel\">";
        // line 264
        echo $this->extensions['Drupal\Core\Template\TwigExtension']->renderVar(t("Clôturer le projet avec code"));
        echo "</h5>
        <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"modal\" aria-label=\"Close\"></button>
      </div>
      <div class=\"modal-body\">
        <div class=\"alert alert-info\">
          <i class=\"fas fa-info-circle me-2\"></i>
          ";
        // line 270
        echo $this->extensions['Drupal\Core\Template\TwigExtension']->renderVar(t("Saisissez le code de validation reçu du prestataire pour clôturer le projet."));
        echo "
        </div>
        <form id=\"formClotureAvecCode\">
          <div class=\"mb-3\">
            <label for=\"codeValidation\" class=\"form-label\">";
        // line 274
        echo $this->extensions['Drupal\Core\Template\TwigExtension']->renderVar(t("Code de validation (6 chiffres)"));
        echo "</label>
            <input type=\"text\" class=\"form-control\" id=\"codeValidation\" name=\"code\" maxlength=\"6\" pattern=\"[0-9]{6}\" inputmode=\"numeric\" autocomplete=\"one-time-code\" placeholder=\"123456\" required>
            <div class=\"form-text\">";
        // line 276
        echo $this->extensions['Drupal\Core\Template\TwigExtension']->renderVar(t("Le prestataire vous a communiqué ce code après avoir terminé le travail."));
        echo "</div>
          </div>
          <div class=\"mb-3\">
            <label for=\"ratingAvecCode\" class=\"form-label\">";
        // line 279
        echo $this->extensions['Drupal\Core\Template\TwigExtension']->renderVar(t("Évaluation du prestataire"));
        echo "</label>
            <div class=\"rating-stars\">
              <input type=\"radio\" name=\"rating\" value=\"5\" id=\"starCode5\"><label for=\"starCode5\">★</label>
              <input type=\"radio\" name=\"rating\" value=\"4\" id=\"starCode4\"><label for=\"starCode4\">★</label>
              <input type=\"radio\" name=\"rating\" value=\"3\" id=\"starCode3\"><label for=\"starCode3\">★</label>
              <input type=\"radio\" name=\"rating\" value=\"2\" id=\"starCode2\"><label for=\"starCode2\">★</label>
              <input type=\"radio\" name=\"rating\" value=\"1\" id=\"starCode1\"><label for=\"starCode1\">★</label>
            </div>
          </div>
          <div class=\"mb-3\">
            <label for=\"commentAvecCode\" class=\"form-label\">";
        // line 289
        echo $this->extensions['Drupal\Core\Template\TwigExtension']->renderVar(t("Commentaire (optionnel)"));
        echo "</label>
            <textarea class=\"form-control\" id=\"commentAvecCode\" name=\"comment\" rows=\"3\" placeholder=\"";
        // line 290
        echo $this->extensions['Drupal\Core\Template\TwigExtension']->renderVar(t("Votre avis sur le travail réalisé..."));
        echo "\"></textarea>
          </div>
        </form>
      </div>
      <div class=\"modal-footer\">
        <button type=\"button\" class=\"btn btn-secondary\" data-bs-dismiss=\"modal\">";
        // line 295
        echo $this->extensions['Drupal\Core\Template\TwigExtension']->renderVar(t("Annuler"));
        echo "</button>
        <button type=\"button\" class=\"btn btn-warning\" id=\"btnConfirmClotureAvecCode\">";
        // line 296
        echo $this->extensions['Drupal\Core\Template\TwigExtension']->renderVar(t("Valider et clôturer"));
        echo "</button>
      </div>
    </div>
  </div>
</div>

";
        // line 303
        echo "<div class=\"modal fade\" id=\"modalCloture\" tabindex=\"-1\" aria-labelledby=\"modalClotureLabel\" aria-hidden=\"true\">
  <div class=\"modal-dialog\">
    <div class=\"modal-content\">
      <div class=\"modal-header\">
        <h5 class=\"modal-title\" id=\"modalClotureLabel\">";
        // line 307
        echo $this->extensions['Drupal\Core\Template\TwigExtension']->renderVar(t("Clôturer le projet"));
        echo "</h5>
        <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"modal\" aria-label=\"Close\"></button>
      </div>
      <div class=\"modal-body\">
        <form id=\"formCloture\">
          <div class=\"mb-3\">
            <label for=\"rating\" class=\"form-label\">";
        // line 313
        echo $this->extensions['Drupal\Core\Template\TwigExtension']->renderVar(t("Évaluation du prestataire"));
        echo "</label>
            <div class=\"rating-stars\">
              <input type=\"radio\" name=\"rating\" value=\"5\" id=\"star5\"><label for=\"star5\">★</label>
              <input type=\"radio\" name=\"rating\" value=\"4\" id=\"star4\"><label for=\"star4\">★</label>
              <input type=\"radio\" name=\"rating\" value=\"3\" id=\"star3\"><label for=\"star3\">★</label>
              <input type=\"radio\" name=\"rating\" value=\"2\" id=\"star2\"><label for=\"star2\">★</label>
              <input type=\"radio\" name=\"rating\" value=\"1\" id=\"star1\"><label for=\"star1\">★</label>
            </div>
          </div>
          <div class=\"mb-3\">
            <label for=\"comment\" class=\"form-label\">";
        // line 323
        echo $this->extensions['Drupal\Core\Template\TwigExtension']->renderVar(t("Commentaire (optionnel)"));
        echo "</label>
            <textarea class=\"form-control\" id=\"comment\" name=\"comment\" rows=\"3\" placeholder=\"";
        // line 324
        echo $this->extensions['Drupal\Core\Template\TwigExtension']->renderVar(t("Votre avis sur le travail réalisé..."));
        echo "\"></textarea>
          </div>
        </form>
      </div>
      <div class=\"modal-footer\">
        <button type=\"button\" class=\"btn btn-secondary\" data-bs-dismiss=\"modal\">";
        // line 329
        echo $this->extensions['Drupal\Core\Template\TwigExtension']->renderVar(t("Annuler"));
        echo "</button>
        <button type=\"button\" class=\"btn btn-warning\" id=\"btnConfirmCloture\">";
        // line 330
        echo $this->extensions['Drupal\Core\Template\TwigExtension']->renderVar(t("Clôturer le projet"));
        echo "</button>
      </div>
    </div>
  </div>
</div>

";
        // line 337
        echo "<div class=\"modal fade\" id=\"modalLiberationFonds\" tabindex=\"-1\" aria-labelledby=\"modalLiberationFondsLabel\" aria-hidden=\"true\">
  <div class=\"modal-dialog\">
    <div class=\"modal-content\">
      <div class=\"modal-header\">
        <h5 class=\"modal-title\" id=\"modalLiberationFondsLabel\">";
        // line 341
        echo $this->extensions['Drupal\Core\Template\TwigExtension']->renderVar(t("Libérer les fonds"));
        echo "</h5>
        <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"modal\" aria-label=\"Close\"></button>
      </div>
      <div class=\"modal-body\">
        <div class=\"alert alert-info\">
          <i class=\"fas fa-info-circle me-2\"></i>
          ";
        // line 347
        echo $this->extensions['Drupal\Core\Template\TwigExtension']->renderVar(t("Cette action va transférer les fonds au prestataire. Cette action est irréversible."));
        echo "
        </div>
        <p>";
        // line 349
        echo $this->extensions['Drupal\Core\Template\TwigExtension']->renderVar(t("Êtes-vous sûr de vouloir libérer les fonds pour ce projet ?"));
        echo "</p>
      </div>
      <div class=\"modal-footer\">
        <button type=\"button\" class=\"btn btn-secondary\" data-bs-dismiss=\"modal\">";
        // line 352
        echo $this->extensions['Drupal\Core\Template\TwigExtension']->renderVar(t("Annuler"));
        echo "</button>
        <button type=\"button\" class=\"btn btn-success\" id=\"btnConfirmLiberationFonds\">";
        // line 353
        echo $this->extensions['Drupal\Core\Template\TwigExtension']->renderVar(t("Libérer les fonds"));
        echo "</button>
      </div>
    </div>
  </div>
</div>
";
        $this->env->getExtension('\Drupal\Core\Template\TwigExtension')
            ->checkDeprecations($context, ["statistiques", "devis_list", "csrf_token_cloture"]);    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName()
    {
        return "modules/custom/client_demandes/templates/client-dashboard.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable()
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo()
    {
        return array (  747 => 353,  743 => 352,  737 => 349,  732 => 347,  723 => 341,  717 => 337,  708 => 330,  704 => 329,  696 => 324,  692 => 323,  679 => 313,  670 => 307,  664 => 303,  655 => 296,  651 => 295,  643 => 290,  639 => 289,  626 => 279,  620 => 276,  615 => 274,  608 => 270,  599 => 264,  593 => 260,  585 => 253,  578 => 249,  574 => 248,  570 => 247,  566 => 246,  562 => 244,  556 => 240,  547 => 236,  541 => 233,  530 => 232,  527 => 231,  524 => 230,  518 => 227,  511 => 226,  509 => 225,  504 => 224,  502 => 223,  497 => 221,  491 => 220,  486 => 218,  479 => 217,  477 => 216,  472 => 214,  467 => 213,  465 => 212,  460 => 210,  456 => 209,  451 => 207,  446 => 206,  443 => 205,  440 => 204,  438 => 203,  433 => 200,  427 => 198,  424 => 197,  418 => 195,  416 => 194,  411 => 193,  409 => 192,  404 => 191,  402 => 190,  397 => 189,  395 => 188,  390 => 187,  388 => 186,  383 => 185,  381 => 184,  376 => 183,  374 => 182,  369 => 181,  366 => 180,  363 => 179,  361 => 178,  355 => 175,  351 => 173,  344 => 170,  339 => 167,  337 => 166,  333 => 165,  327 => 162,  322 => 160,  318 => 158,  314 => 157,  307 => 153,  303 => 152,  299 => 151,  295 => 150,  291 => 149,  285 => 145,  283 => 144,  278 => 141,  272 => 139,  270 => 138,  266 => 137,  259 => 132,  244 => 119,  236 => 114,  228 => 109,  215 => 99,  210 => 97,  205 => 95,  198 => 90,  195 => 88,  192 => 86,  181 => 78,  175 => 75,  169 => 72,  165 => 71,  159 => 68,  155 => 67,  147 => 62,  134 => 52,  130 => 51,  124 => 48,  120 => 47,  112 => 42,  104 => 36,  101 => 35,  93 => 29,  88 => 27,  83 => 25,  76 => 21,  71 => 19,  65 => 16,  60 => 14,  50 => 7,  46 => 6,  42 => 5,  39 => 4,);
    }

    public function getSourceContext()
    {
        return new Source("{#
  Template : client_dashboard.html.twig - Dashboard Client
#}

{{ attach_library(\"client_demandes/dashboard\") }}
{{ attach_library(\"client_demandes/chat\") }}
{{ attach_library(\"bootstrap5/bootstrap5-js-latest\") }}

<div class=\"client-dashboard container py-4\">
  <div class=\"d-flex justify-content-between align-items-center mb-4\">
    <div>
      <h1>
        <i class=\"fas fa-tachometer-alt me-2\"></i>
        {{ \"Dashboard Client\"|t }}
      </h1>
      <p class=\"text-muted\">{{ \"Gérez vos demandes et suivez vos devis\"|t }}</p>
    </div>
    <div class=\"btn-group\">
      <a href=\"{{ path(\"client_demandes.nouvelle_demande\") }}\" class=\"btn btn-success\">
        <i class=\"fas fa-plus me-2\"></i>
        {{ \"Faire une demande\"|t }}
      </a>
      <a href=\"/client/mes-demandes\" class=\"btn btn-outline-secondary\">
        <i class=\"fas fa-list me-2\"></i>
        {{ \"Mes demandes\"|t }}
      </a>
      <a href=\"{{ path(\"client_demandes.client_commerce_orders\") }}\" class=\"btn btn-outline-info\">
        <i class=\"fas fa-shopping-cart me-2\"></i>
        {{ \"Mes commandes\"|t }}
      </a>
    </div>
  </div>

  {# Statistiques #}
  {% if statistiques %}
    <div class=\"row g-4 mb-5\">
      <div class=\"col-md-6\">
        <div class=\"card shadow-sm\">
          <div class=\"card-header bg-primary text-white\">
            <h5 class=\"mb-0\">
              <i class=\"fas fa-chart-line me-2\"></i>
              {{ \"Activité\"|t }}
            </h5>
          </div>
          <div class=\"card-body\">
            <div class=\"d-flex justify-content-between mb-3\">
              <span>{{ \"Demandes créées\"|t }}</span>
              <strong>{{ statistiques.total_demandes }}</strong>
            </div>
            <div class=\"d-flex justify-content-between\">
              <span>{{ \"Devis reçus\"|t }}</span>
              <strong>{{ statistiques.devis_recus }}</strong>
            </div>
          </div>
        </div>
      </div>
      <div class=\"col-md-6\">
        <div class=\"card shadow-sm\">
          <div class=\"card-header bg-success text-white\">
            <h5 class=\"mb-0\">
              <i class=\"fas fa-euro-sign me-2\"></i>
              {{ \"Finances\"|t }}
            </h5>
          </div>
          <div class=\"card-body\">
            <div class=\"d-flex justify-content-between mb-3\">
              <span>{{ \"Projets payés\"|t }}</span>
              <strong>{{ statistiques.devis_payes }}</strong>
            </div>
            <div class=\"d-flex justify-content-between mb-3\">
              <span>{{ \"Total dépensé\"|t }}</span>
              <strong>{{ statistiques.montant_total_depense|number_format(0, \",\", \" \") }}€</strong>
            </div>
            <div class=\"d-flex justify-content-between\">
              <span>{{ \"Dépôts effectués\"|t }}</span>
              <strong class=\"text-warning\">
                <i class=\"fas fa-piggy-bank me-1\"></i>
                {{ statistiques.montant_depots|number_format(0, \",\", \" \") }}€
              </strong>
            </div>
          </div>
        </div>
      </div>
    </div>
  {% endif %}

  {# Section principale #}
  <div class=\"row g-4\">
    {# Chat #}
    <div class=\"col-lg-4\">
      <div class=\"card shadow-sm h-100\">
        <div class=\"card-header bg-primary text-white d-flex justify-content-between align-items-center\">
          <h5 class=\"mb-0\">
            <i class=\"fas fa-comments me-2\"></i>
            {{ \"Messages\"|t }}
          </h5>
          <button class=\"btn btn-outline-light btn-sm chat-history-toggle-btn\" title=\"{{ 'Voir l\\'historique des conversations'|t }}\">
            <i class=\"fas fa-history me-1\"></i>
            {{ 'Historique'|t }}
          </button>
        </div>
        <div class=\"card-body p-0\">
          <div class=\"chat-container-embedded\">
            <div class=\"conversations-list-embedded\">
              <!-- Les conversations seront chargées ici via AJAX -->
            </div>
            <div class=\"chat-window-embedded\">
              <div class=\"chat-window-header-embedded\">
                {{ \"Sélectionnez une conversation\"|t }}
              </div>
              <div class=\"messages-list-embedded\">
                <div class=\"no-messages text-center py-4\">
                  <i class=\"fas fa-comments fa-2x text-muted mb-2\"></i>
                  <p class=\"text-muted\">{{ \"Aucune conversation sélectionnée\"|t }}</p>
                </div>
              </div>
              <div class=\"chat-message-form-embedded\" style=\"display: none;\">
                <form class=\"d-flex\">
                  <input type=\"text\" class=\"form-control chat-message-input\" placeholder=\"{{ 'Tapez votre message...'|t }}\">
                  <button type=\"submit\" class=\"btn btn-primary chat-send-button\">
                    <i class=\"fas fa-paper-plane\"></i>
                  </button>
                </form>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    {# Devis reçus #}
    <div class=\"col-lg-8\">
      <div class=\"card shadow-sm\">
        <div class=\"card-header bg-info text-white\">
          <h5 class=\"mb-0\">
            <i class=\"fas fa-file-invoice me-2\"></i>
            {{ \"Devis reçus - Liste\"|t }}
            {% if devis_list %}
              <span class=\"badge bg-light text-dark ms-2\">{{ devis_list|length }}</span>
            {% endif %}
          </h5>
        </div>
        <div class=\"card-body\">
          {% if devis_list and devis_list|length > 0 %}
            <div class=\"table-responsive\">
              <table class=\"table table-hover\">
                <thead class=\"table-dark\">
                  <tr>
                    <th>{{ \"Prestataire\"|t }}</th>
                    <th>{{ \"Prix\"|t }}</th>
                    <th>{{ \"Date\"|t }}</th>
                    <th>{{ \"Statut\"|t }}</th>
                    <th>{{ \"Actions\"|t }}</th>
                  </tr>
                </thead>
                <tbody>
                  {% for devis in devis_list %}
                    <tr>
                      <td>
                        <strong>{{ devis.prestataire_name|default(\"Prestataire\") }}</strong>
                        <br>
                        <small class=\"text-muted\">{{ devis.demande_title|default(\"Demande\") }}</small>
                      </td>
                      <td>
                        <strong>{{ devis.field_price.value|default(0)|number_format(0, \",\", \" \") }}€</strong>
                        {% if devis.reste_a_payer is defined and devis.reste_a_payer > 0 %}
                          <br>
                          <small class=\"text-warning\">
                            <i class=\"fas fa-exclamation-triangle me-1\"></i>
                            {{ \"Reste: \"|t }}{{ devis.reste_a_payer|number_format(0, \",\", \" \") }}€
                          </small>
                        {% endif %}
                      </td>
                      <td>
                        <small>{{ devis.created.value|date(\"d/m/Y\") }}</small>
                      </td>
                      <td>
                        {% if devis.field_status and devis.field_status.value %}
                          {% set status = devis.field_status.value %}
                          {% if status == \"pending\" %}
                            <span class=\"badge bg-warning\">{{ \"En attente\"|t }}</span>
                          {% elseif status == \"accepted\" %}
                            <span class=\"badge bg-success\">{{ \"Accepté\"|t }}</span>
                          {% elseif status == \"paid\" %}
                            <span class=\"badge bg-primary\">{{ \"Payé\"|t }}</span>
                          {% elseif status == \"partial_paid\" %}
                            <span class=\"badge bg-info\">{{ \"Partiellement payé\"|t }}</span>
                          {% elseif status == \"completed\" %}
                            <span class=\"badge bg-success\">{{ \"Terminé\"|t }}</span>
                          {% elseif status == \"ready_to_close\" %}
                            <span class=\"badge bg-warning\">{{ \"Prêt à clôturer\"|t }}</span>
                          {% elseif status == \"closed\" %}
                            <span class=\"badge bg-dark\">{{ \"Clôturé\"|t }}</span>
                          {% elseif status == \"refused\" %}
                            <span class=\"badge bg-danger\">{{ \"Refusé\"|t }}</span>
                          {% endif %}
                        {% else %}
                          <span class=\"badge bg-secondary\">{{ \"Inconnu\"|t }}</span>
                        {% endif %}
                      </td>
                      <td>
                        <div class=\"btn-group-sm\">
                          {% if devis.field_status and devis.field_status.value %}
                            {% set status = devis.field_status.value %}
                            {% if status == \"pending\" %}
                              <a href=\"/client/devis/{{ devis.id }}/accepter\" class=\"btn btn-success btn-sm\">
                                <i class=\"fas fa-check me-1\"></i>{{ \"Accepter\"|t }}
                              </a>
                              <a href=\"/client/devis/{{ devis.id }}/refuser\" class=\"btn btn-danger btn-sm\">
                                <i class=\"fas fa-times me-1\"></i>{{ \"Refuser\"|t }}
                              </a>
                            {% elseif status == \"accepted\" %}
                              <a href=\"/client/paiement-options/{{ devis.id }}\" class=\"btn btn-primary btn-sm\">
                                <i class=\"fas fa-credit-card me-1\"></i>{{ \"Payer\"|t }}
                              </a>
                            {% elseif status == \"paid\" or status == \"depot\" or status == \"completed\" %}
                              <button class=\"btn btn-warning btn-sm btn-cloture-projet\" data-devis-id=\"{{ devis.id }}\" data-csrf-token=\"{{ csrf_token_cloture }}\">
                                <i class=\"fas fa-flag-checkered me-1\"></i>{{ \"Clôturer le projet\"|t }}
                              </button>
                              <button class=\"btn btn-info btn-sm btn-cloture-avec-code\" data-devis-id=\"{{ devis.id }}\" data-csrf-token=\"{{ csrf_token_cloture }}\">
                                <i class=\"fas fa-key me-1\"></i>{{ \"Clôturer avec code\"|t }}
                              </button>
                            {% elseif status == \"ready_to_close\" %}
                              <span class=\"text-muted small\">{{ \"En attente de validation prestataire\"|t }}</span>
                            {% elseif status == \"closed\" %}
                              <button class=\"btn btn-success btn-sm btn-liberer-fonds\" data-devis-id=\"{{ devis.id }}\" data-csrf-token=\"{{ csrf_token_cloture }}\">
                                <i class=\"fas fa-money-bill-wave me-1\"></i>{{ \"Libérer les fonds\"|t }}
                              </button>
                            {% endif %}
                          {% endif %}
                          {% if devis.field_demande_ref and devis.field_demande_ref.target_id and devis.uid and devis.uid.target_id %}
                            <button class=\"btn btn-outline-primary btn-sm\" onclick=\"openChatFromButton({{ devis.field_demande_ref.target_id }}, {{ devis.uid.target_id }}, '{{ devis.prestataire_name|default(\"Prestataire\") }}', '{{ devis.demande_title|default(\"Demande\") }}', 'prestataire')\">
                              <i class=\"fas fa-comments me-1\"></i>{{ \"Chat\"|t }}
                            </button>
                          {% endif %}
                        </div>
                      </td>
                    </tr>
                  {% endfor %}
                </tbody>
              </table>
            </div>
          {% else %}
            <div class=\"text-center py-5\">
              <i class=\"fas fa-inbox fa-3x text-muted mb-3\"></i>
              <h5 class=\"text-muted\">{{ \"Aucun devis reçu\"|t }}</h5>
              <p class=\"text-muted\">{{ \"Créez une demande pour recevoir des devis de prestataires\"|t }}</p>
              <a href=\"{{ path(\"client_demandes.nouvelle_demande\") }}\" class=\"btn btn-success\">
                <i class=\"fas fa-plus me-2\"></i>{{ \"Créer une demande\"|t }}
              </a>
            </div>
          {% endif %}
        </div>
      </div>
    </div>
  </div>
</div>

{# Modal de clôture avec code #}
<div class=\"modal fade\" id=\"modalClotureAvecCode\" tabindex=\"-1\" aria-labelledby=\"modalClotureAvecCodeLabel\" aria-hidden=\"true\">
  <div class=\"modal-dialog\">
    <div class=\"modal-content\">
      <div class=\"modal-header\">
        <h5 class=\"modal-title\" id=\"modalClotureAvecCodeLabel\">{{ 'Clôturer le projet avec code'|t }}</h5>
        <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"modal\" aria-label=\"Close\"></button>
      </div>
      <div class=\"modal-body\">
        <div class=\"alert alert-info\">
          <i class=\"fas fa-info-circle me-2\"></i>
          {{ 'Saisissez le code de validation reçu du prestataire pour clôturer le projet.'|t }}
        </div>
        <form id=\"formClotureAvecCode\">
          <div class=\"mb-3\">
            <label for=\"codeValidation\" class=\"form-label\">{{ 'Code de validation (6 chiffres)'|t }}</label>
            <input type=\"text\" class=\"form-control\" id=\"codeValidation\" name=\"code\" maxlength=\"6\" pattern=\"[0-9]{6}\" inputmode=\"numeric\" autocomplete=\"one-time-code\" placeholder=\"123456\" required>
            <div class=\"form-text\">{{ 'Le prestataire vous a communiqué ce code après avoir terminé le travail.'|t }}</div>
          </div>
          <div class=\"mb-3\">
            <label for=\"ratingAvecCode\" class=\"form-label\">{{ 'Évaluation du prestataire'|t }}</label>
            <div class=\"rating-stars\">
              <input type=\"radio\" name=\"rating\" value=\"5\" id=\"starCode5\"><label for=\"starCode5\">★</label>
              <input type=\"radio\" name=\"rating\" value=\"4\" id=\"starCode4\"><label for=\"starCode4\">★</label>
              <input type=\"radio\" name=\"rating\" value=\"3\" id=\"starCode3\"><label for=\"starCode3\">★</label>
              <input type=\"radio\" name=\"rating\" value=\"2\" id=\"starCode2\"><label for=\"starCode2\">★</label>
              <input type=\"radio\" name=\"rating\" value=\"1\" id=\"starCode1\"><label for=\"starCode1\">★</label>
            </div>
          </div>
          <div class=\"mb-3\">
            <label for=\"commentAvecCode\" class=\"form-label\">{{ 'Commentaire (optionnel)'|t }}</label>
            <textarea class=\"form-control\" id=\"commentAvecCode\" name=\"comment\" rows=\"3\" placeholder=\"{{ 'Votre avis sur le travail réalisé...'|t }}\"></textarea>
          </div>
        </form>
      </div>
      <div class=\"modal-footer\">
        <button type=\"button\" class=\"btn btn-secondary\" data-bs-dismiss=\"modal\">{{ 'Annuler'|t }}</button>
        <button type=\"button\" class=\"btn btn-warning\" id=\"btnConfirmClotureAvecCode\">{{ 'Valider et clôturer'|t }}</button>
      </div>
    </div>
  </div>
</div>

{# Modal de clôture de projet #}
<div class=\"modal fade\" id=\"modalCloture\" tabindex=\"-1\" aria-labelledby=\"modalClotureLabel\" aria-hidden=\"true\">
  <div class=\"modal-dialog\">
    <div class=\"modal-content\">
      <div class=\"modal-header\">
        <h5 class=\"modal-title\" id=\"modalClotureLabel\">{{ 'Clôturer le projet'|t }}</h5>
        <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"modal\" aria-label=\"Close\"></button>
      </div>
      <div class=\"modal-body\">
        <form id=\"formCloture\">
          <div class=\"mb-3\">
            <label for=\"rating\" class=\"form-label\">{{ 'Évaluation du prestataire'|t }}</label>
            <div class=\"rating-stars\">
              <input type=\"radio\" name=\"rating\" value=\"5\" id=\"star5\"><label for=\"star5\">★</label>
              <input type=\"radio\" name=\"rating\" value=\"4\" id=\"star4\"><label for=\"star4\">★</label>
              <input type=\"radio\" name=\"rating\" value=\"3\" id=\"star3\"><label for=\"star3\">★</label>
              <input type=\"radio\" name=\"rating\" value=\"2\" id=\"star2\"><label for=\"star2\">★</label>
              <input type=\"radio\" name=\"rating\" value=\"1\" id=\"star1\"><label for=\"star1\">★</label>
            </div>
          </div>
          <div class=\"mb-3\">
            <label for=\"comment\" class=\"form-label\">{{ 'Commentaire (optionnel)'|t }}</label>
            <textarea class=\"form-control\" id=\"comment\" name=\"comment\" rows=\"3\" placeholder=\"{{ 'Votre avis sur le travail réalisé...'|t }}\"></textarea>
          </div>
        </form>
      </div>
      <div class=\"modal-footer\">
        <button type=\"button\" class=\"btn btn-secondary\" data-bs-dismiss=\"modal\">{{ 'Annuler'|t }}</button>
        <button type=\"button\" class=\"btn btn-warning\" id=\"btnConfirmCloture\">{{ 'Clôturer le projet'|t }}</button>
      </div>
    </div>
  </div>
</div>

{# Modal de libération des fonds #}
<div class=\"modal fade\" id=\"modalLiberationFonds\" tabindex=\"-1\" aria-labelledby=\"modalLiberationFondsLabel\" aria-hidden=\"true\">
  <div class=\"modal-dialog\">
    <div class=\"modal-content\">
      <div class=\"modal-header\">
        <h5 class=\"modal-title\" id=\"modalLiberationFondsLabel\">{{ 'Libérer les fonds'|t }}</h5>
        <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"modal\" aria-label=\"Close\"></button>
      </div>
      <div class=\"modal-body\">
        <div class=\"alert alert-info\">
          <i class=\"fas fa-info-circle me-2\"></i>
          {{ 'Cette action va transférer les fonds au prestataire. Cette action est irréversible.'|t }}
        </div>
        <p>{{ 'Êtes-vous sûr de vouloir libérer les fonds pour ce projet ?'|t }}</p>
      </div>
      <div class=\"modal-footer\">
        <button type=\"button\" class=\"btn btn-secondary\" data-bs-dismiss=\"modal\">{{ 'Annuler'|t }}</button>
        <button type=\"button\" class=\"btn btn-success\" id=\"btnConfirmLiberationFonds\">{{ 'Libérer les fonds'|t }}</button>
      </div>
    </div>
  </div>
</div>
", "modules/custom/client_demandes/templates/client-dashboard.html.twig", "/var/www/html/web/modules/custom/client_demandes/templates/client-dashboard.html.twig");
    }
    
    public function checkSecurity()
    {
        static $tags = array("if" => 35, "for" => 157, "set" => 179);
        static $filters = array("escape" => 5, "t" => 14, "number_format" => 72, "length" => 139, "default" => 160, "date" => 175);
        static $functions = array("attach_library" => 5, "path" => 19);

        try {
            $this->sandbox->checkSecurity(
                ['if', 'for', 'set'],
                ['escape', 't', 'number_format', 'length', 'default', 'date'],
                ['attach_library', 'path']
            );
        } catch (SecurityError $e) {
            $e->setSourceContext($this->source);

            if ($e instanceof SecurityNotAllowedTagError && isset($tags[$e->getTagName()])) {
                $e->setTemplateLine($tags[$e->getTagName()]);
            } elseif ($e instanceof SecurityNotAllowedFilterError && isset($filters[$e->getFilterName()])) {
                $e->setTemplateLine($filters[$e->getFilterName()]);
            } elseif ($e instanceof SecurityNotAllowedFunctionError && isset($functions[$e->getFunctionName()])) {
                $e->setTemplateLine($functions[$e->getFunctionName()]);
            }

            throw $e;
        }

    }
}
