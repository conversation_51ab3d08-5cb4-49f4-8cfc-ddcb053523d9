<?php

namespace Drupal\client_demandes\Controller;

use <PERSON><PERSON>al\Core\Controller\ControllerBase;
use <PERSON><PERSON>al\Core\Session\AccountInterface;
use <PERSON><PERSON><PERSON>\node\Entity\Node;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Dompdf\Dompdf;
use Dompdf\Options;
use Drupal\Core\Mail\MailManagerInterface;
use Drupal\user\Entity\User;
use Drupal\Core\Pager\PagerManagerInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Dr<PERSON>al\Core\Access\CsrfTokenGenerator;

use Drupal\Core\Access\AccessResult;
use Drupal\Core\Messenger\MessengerTrait;
use Drupal\client_demandes\Form\DepotCodeValidateForm;



/**
 * Contrôleur pour le dashboard des clients.
 */
class ClientController extends ControllerBase
{

  /**
   * The current user.
   *
   * @var \Drupal\Core\Session\AccountInterface
   */
  protected $currentUser;

  /**
   * Constructs a new ClientController object.
   */
  public function __construct(AccountInterface $current_user)
  {
    $this->currentUser = $current_user;
  }

  /**
   * {@inheritdoc}
   */
  public static function create(ContainerInterface $container)
  {
    return new static(
      $container->get('current_user')
    );
  }




  public function dashboard()
  {
    if ($this->currentUser->isAnonymous()) {
      return $this->redirect('user.login');
    }

    $demandes_query = \Drupal::entityQuery('node')
      ->accessCheck(FALSE)
      ->condition('type', 'demande')
      ->condition('uid', $this->currentUser->id())
      ->condition('status', 1)
      ->sort('created', 'DESC')
      ->pager(10);
    $demande_ids = $demandes_query->execute();
    $demandes = Node::loadMultiple($demande_ids);


    $demandes = Node::loadMultiple($demande_ids);

    foreach ($demandes as $demande) {
      if ($demande->hasField('field_categories_services') && !$demande->get('field_categories_services')->isEmpty()) {
        $terms = $demande->field_categories_services->referencedEntities();
        $noms_categories = [];
        foreach ($terms as $term) {
          $noms_categories[] = $term->label();
        }
        $demande->noms_categories = $noms_categories;
      } else {
        $demande->noms_categories = ['Non catégorisé'];
      }
    }


    $devis_query = \Drupal::entityQuery('node')
      ->accessCheck(FALSE)
      ->condition('type', 'devis')
      ->condition('status', 1)
      ->sort('created', 'DESC')
      ->range(0, 50);
    $devis_ids = $devis_query->execute();
    $all_devis = Node::loadMultiple($devis_ids);

    $devis = [];
    $tokens = [];
    $demandes_non_editables = [];
    $reste_a_payer_total = 0;
    $token_service = \Drupal::service('csrf_token');

    foreach ($all_devis as $devis_node) {
      if (
        $devis_node->hasField('field_demande_ref') &&
        !$devis_node->get('field_demande_ref')->isEmpty()
      ) {
        $demande_ref = $devis_node->get('field_demande_ref')->entity;

        if ($demande_ref && $demande_ref->getOwnerId() == $this->currentUser->id()) {
          $status = $devis_node->get('field_status')->value ?? 'pending';

          // Vérifier si une commande est associée et complétée
          $order_status = $this->checkOrderStatus($devis_node);
          if ($order_status === 'completed' && $status === 'paid') {
            $status = 'ready_to_close';
          }

          if (in_array($status, ['accepted', 'paid', 'partial_paid', 'depot', 'ready_to_close'])) {
            $demandes_non_editables[] = $demande_ref->id();
          }

          if ($status === 'partial_paid') {
            // Utiliser le service Commerce pour calculer le montant restant
            $commerce_service = \Drupal::service('client_demandes.commerce_order_service');
            $prix_total = (float) $devis_node->get('field_price')->value ?? 0;
            $montant_paye = $commerce_service->getMontantDejaPaye($devis_node->id());
            $reste = max(0, $prix_total - $montant_paye);
            $devis_node->reste_a_payer = $reste;
            $devis_node->montant_paye = $montant_paye;
            $reste_a_payer_total += $reste;
          }

          if ($status !== 'refused') {
            $tokens[$devis_node->id()] = $token_service->get('/client/cloture-projet');
            $devis[] = $devis_node;
          }
        }
      }
    }

    if (empty($devis)) {
      $devis = $this->getDevisDemo();
    }

    $demandes_non_editables = array_unique($demandes_non_editables);
    $statistiques = $this->getStatistiques();
    $statistiques['reste_a_payer'] = $reste_a_payer_total;

    // Préparer les options de filtres
    $filter_options = $this->prepareFilterOptions($devis);

    // Générer le token CSRF pour la clôture
    $csrf_token_cloture = $token_service->get('/client/cloture-projet');


    $forms = [];
    foreach ($devis as $d) { // <-- $devis, pas $devis_proposes
      $forms[(string) $d->id()] = \Drupal::formBuilder()->getForm(
        DepotCodeValidateForm::class,
        (int) $d->id()
      );
    }


    return [
      '#theme' => 'client_dashboard',
      '#demandes' => $demandes,
      '#demandes_list' => $demandes,
      '#devis_proposes' => $devis,
      '#depot_code_forms' => $forms,
      '#devis_list' => $devis,
      '#devis_table' => $devis,
      '#statistiques' => $statistiques,
      '#tokens' => $tokens,
      '#demandes_non_editables' => $demandes_non_editables,
      '#filter_options' => $filter_options,
      '#csrf_token_cloture' => $csrf_token_cloture,
      '#debug_info' => [
        'total_devis_found' => count($all_devis),
        'devis_after_filter' => count($devis),
        'current_user_id' => $this->currentUser->id(),
        'is_demo_data' => empty($all_devis) ? 'Oui' : 'Non',
        'demandes_non_editables' => $demandes_non_editables,
      ],
      '#attached' => [
        'library' => [
          'client_demandes/dashboard',
        ],
      ],
      '#cache' => ['max-age' => 0],
    ];
  }




  /**
   * Page pour créer une nouvelle demande.
   */
  public function nouvelleDemande()
  {
    // Rediriger vers le formulaire de création de demande
    return $this->redirect('node.add', ['node_type' => 'demande']);
  }

  /**
   * Page pour voir les demandes du client.
   */
  public function mesDemandes($user = null)
  {
    // Si aucun utilisateur spécifié, utiliser l'utilisateur connecté
    $user_id = $user ? $user : $this->currentUser->id();

    // Vérifier que l'utilisateur peut voir ces demandes
    if ($user_id != $this->currentUser->id() && !$this->currentUser->hasPermission('administer site configuration')) {
      throw new AccessDeniedHttpException();
    }

    // Récupérer toutes les demandes du client
    $demandes_query = \Drupal::entityQuery('node')
      ->accessCheck(FALSE)
      ->condition('type', 'demande')
      ->condition('uid', $user_id)
      ->condition('status', 1)
      ->sort('created', 'DESC');

    $demande_ids = $demandes_query->execute();



    $demandes = Node::loadMultiple($demande_ids);

    foreach ($demandes as $demande) {
      if ($demande->hasField('field_categories_services') && !$demande->get('field_categories_services')->isEmpty()) {
        $terms = $demande->field_categories_services->referencedEntities();
        $noms_categories = [];
        foreach ($terms as $term) {
          $noms_categories[] = $term->label();
        }
        $demande->noms_categories = $noms_categories;
      } else {
        $demande->noms_categories = ['Non catégorisé'];
      }
    }

    return [
      '#theme' => 'client_mes_demandes',
      '#demandes' => $demandes,
      '#user_id' => $user_id,
      '#cache' => [
        'max-age' => 0,
      ],
    ];
  }

  /**
   * Récupère les vraies données de devis (plus de demo).
   */
  private function getDevisDemo()
  {
    // Retourner un tableau vide - plus de données de démonstration
    return [];
  }



  /**
   * Récupère la première catégorie de service disponible.
   */
  private function getFirstCategoryService()
  {
    $terms = \Drupal::entityQuery('taxonomy_term')
      ->condition('vid', 'categories_services')
      ->accessCheck(FALSE)
      ->execute();

    if (!empty($terms)) {
      $term_ids = array_values($terms);
      return reset($term_ids);
    }

    return null;
  }

  /**
   * Télécharge la facture d'un devis payé.
   */
  public function downloadInvoice($devis)
  {
    $devis_node = Node::load($devis);

    if (!$devis_node || $devis_node->getType() !== 'devis') {
      throw new NotFoundHttpException();
    }

    // Vérifier que le devis appartient au client connecté
    $demande = $devis_node->get('field_demande_ref')->entity;
    if (!$demande || $demande->getOwnerId() != $this->currentUser->id()) {
      throw new AccessDeniedHttpException();
    }

    // Vérifier que le devis est payé
    $status = $devis_node->hasField('field_status') ? $devis_node->get('field_status')->value : 'pending';
    if ($status !== 'paid') {
      \Drupal::messenger()->addError($this->t('La facture n\'est disponible que pour les devis payés.'));
      return $this->redirect('client_demandes.client_dashboard');
    }

    // Générer et retourner la facture (PDF)
    $invoice_content = $this->generateInvoicePdf($devis_node);

    $response = new Response($invoice_content);
    $response->headers->set('Content-Type', 'application/pdf');
    $response->headers->set('Content-Disposition', 'attachment; filename="facture-' . $devis_node->id() . '.pdf"');

    return $response;
  }

  /**
   * Génère le PDF de la facture.
   */
  private function generateInvoicePdf($devis_node)
  {
    // Pour l'instant, retourner un PDF simple
    // Dans une implémentation complète, utiliser une librairie comme TCPDF ou mPDF

    $content = "FACTURE\n\n";
    $content .= "Devis: " . $devis_node->getTitle() . "\n";
    $content .= "Montant: " . $devis_node->get('field_price')->value . " €\n";
    $content .= "Date: " . date('d/m/Y') . "\n";
    $content .= "Statut: Payé\n\n";
    $content .= "Merci pour votre confiance !";

    return $content;
  }





  /**
   * Statistiques du client.
   */
  private function getStatistiques()
  {
    $user_id = $this->currentUser->id();

    // Nombre total de demandes
    $total_demandes = \Drupal::entityQuery('node')
      ->accessCheck(FALSE)
      ->condition('type', 'demande')
      ->condition('uid', $user_id)
      ->condition('status', 1)
      ->count()
      ->execute();

    // Récupérer toutes les demandes du client pour calculer les projets verrouillés
    $demandes_query = \Drupal::entityQuery('node')
      ->accessCheck(FALSE)
      ->condition('type', 'demande')
      ->condition('uid', $user_id)
      ->condition('status', 1);

    $demande_ids = $demandes_query->execute();
    // $demandes = Node::loadMultiple($demande_ids);


    $demandes = Node::loadMultiple($demande_ids);

    foreach ($demandes as $demande) {
      if ($demande->hasField('field_categories_services') && !$demande->get('field_categories_services')->isEmpty()) {
        $terms = $demande->field_categories_services->referencedEntities();
        $noms_categories = [];
        foreach ($terms as $term) {
          $noms_categories[] = $term->label();
        }
        $demande->noms_categories = $noms_categories;
      } else {
        $demande->noms_categories = ['Non catégorisé'];
      }
    }




    // Récupérer tous les devis pour les demandes du client
    $devis_query = \Drupal::entityQuery('node')
      ->accessCheck(FALSE)
      ->condition('type', 'devis')
      ->condition('status', 1);

    $all_devis_ids = $devis_query->execute();
    $all_devis = Node::loadMultiple($all_devis_ids);

    $devis_recus = 0;
    $devis_acceptes = 0;
    $devis_payes = 0;
    $montant_total_depense = 0;
    $montant_en_cours = 0;
    $demandes_verrouillees = []; // Pour compter les projets payés (demandes verrouillées)

    foreach ($all_devis as $devis_node) {
      if ($devis_node->hasField('field_demande_ref') && !$devis_node->get('field_demande_ref')->isEmpty()) {
        $demande_ref = $devis_node->get('field_demande_ref')->entity;
        if ($demande_ref && $demande_ref->getOwnerId() == $user_id) {
          $devis_recus++;

          $status = $devis_node->hasField('field_status') ? $devis_node->get('field_status')->value : 'pending';
          $prix = $devis_node->hasField('field_price') && !$devis_node->get('field_price')->isEmpty()
            ? (float) $devis_node->get('field_price')->value
            : 0;

          if ($status === 'accepted') {
            $devis_acceptes++;
            $montant_en_cours += $prix;
            // Marquer la demande comme verrouillée (projet avec devis accepté)
            $demandes_verrouillees[] = $demande_ref->id();
          } elseif ($status === 'partial_paid') {
            $devis_acceptes++; // Compter comme accepté pour les stats
            $montant_en_cours += $prix; // Montant en cours car pas entièrement payé
            // Marquer la demande comme verrouillée (projet avec paiement partiel)
            $demandes_verrouillees[] = $demande_ref->id();
          } elseif ($status === 'paid') {
            $devis_payes++;
            // Seuls les devis entièrement payés comptent dans le total dépensé
            $montant_total_depense += $prix;
            // Marquer la demande comme verrouillée (projet avec devis payé)
            $demandes_verrouillees[] = $demande_ref->id();
          }
        }
      }
    }

    // Compter les projets payés = nombre de demandes verrouillées (uniques)
    $projets_payes = count(array_unique($demandes_verrouillees));

    // Calculer le montant des dépôts effectués
    $commerce_service = \Drupal::service('client_demandes.commerce_order_service');
    $montant_depots = $commerce_service->getMontantDepotsClient($user_id);

    // Debug: Logger les statistiques calculées
    \Drupal::logger('client_demandes')->info(
      'Statistiques client @user_id - Projets payés: @projets, Total dépensé: @total, Dépôts: @depots, Demandes verrouillées: @demandes',
      [
        '@user_id' => $user_id,
        '@projets' => $projets_payes,
        '@total' => $montant_total_depense,
        '@depots' => $montant_depots,
        '@demandes' => implode(',', array_unique($demandes_verrouillees)),
      ]
    );

    // Calcul du taux d'acceptation
    $taux_acceptation = $devis_recus > 0 ? round(($devis_acceptes / $devis_recus) * 100, 1) : 0;

    return [
      'total_demandes' => $total_demandes,
      'devis_recus' => $devis_recus,
      'devis_acceptes' => $devis_acceptes,
      'devis_payes' => $projets_payes, // Nombre de demandes verrouillées (projets payés)
      'montant_total_depense' => $montant_total_depense, // Somme des field_price des devis payés
      'montant_en_cours' => $montant_en_cours,
      'montant_depots' => $montant_depots, // Montant des dépôts effectués
      'taux_acceptation' => $taux_acceptation,
    ];
  }





  public function clotureProjet(Request $request): JsonResponse
  {
    if ($this->currentUser->isAnonymous()) {
      return new JsonResponse(['success' => false, 'message' => $this->t('Non authentifié.')], 403);
    }

    $devis_id = (int) $request->request->get('devis_id');
    $rating   = (int) $request->request->get('rating', 0);
    $comment  = (string) $request->request->get('comment', '');

    if (!$devis_id) {
      return new JsonResponse(['success' => false, 'message' => $this->t('ID de devis manquant.')], 400);
    }

    $devis = Node::load($devis_id);
    if (!$devis || $devis->bundle() !== 'devis') {
      return new JsonResponse(['success' => false, 'message' => $this->t('Devis introuvable.')], 404);
    }

    // Le client qui clôture doit être le propriétaire de la demande liée au devis.
    $demande = $devis->get('field_demande_ref')->entity ?? NULL;
    if (!$demande || (int) $demande->getOwnerId() !== (int) $this->currentUser->id()) {
      return new JsonResponse(['success' => false, 'message' => $this->t('Accès refusé.')], 403);
    }

    $status = $devis->get('field_status')->value ?? 'pending';
    if (!in_array($status, ['paid', 'depot', 'ready_to_close'], true)) {
      return new JsonResponse(['success' => false, 'message' => $this->t('Statut incompatible avec la clôture.')], 400);
    }

    // Enregistrer rating/comment si disponibles.
    if ($devis->hasField('field_rating') && $rating > 0) {
      $devis->set('field_rating', $rating);
    }
    if ($devis->hasField('field_review') && $comment !== '') {
      $devis->set('field_review', $comment);
    }

    // Générer un code OTP à transmettre au prestataire (durée 15 min, 3 essais)
    $code    = (string) random_int(100000, 999999);
    $expires = \Drupal::time()->getRequestTime() + 15 * 60;
    $attempts = 3;

    // Stockage OTP côté serveur (KeyValue, pas besoin de champs).
    $kv = \Drupal::keyValue('client_demandes_otp');
    $kv->set("devis:$devis_id:otp", [
      'code' => $code,
      'expires' => $expires,
      'attempts' => $attempts,
      'client_uid' => (int) $this->currentUser->id(),
      'prestataire_uid' => (int) ($devis->getOwnerId() ?? 0),
    ]);

    // Marquer "prêt à clôturer" si on vient de paid/depot.
    if ($status !== 'ready_to_close') {
      $devis->set('field_status', 'ready_to_close');
    }
    $devis->save();

    // Réponse JSON
    return new JsonResponse([
      'success'  => true,
      'code'     => $code,
      'expires'  => $expires,
      'attempts' => $attempts,
    ]);
  }
  /**
   * Clôture un projet avec validation par code OTP.
   */
  public function clotureProjetAvecCode(Request $request, $devis_id): JsonResponse
  {
    if ($this->currentUser->isAnonymous()) {
      return new JsonResponse(['success' => false, 'message' => $this->t('Non authentifié.')], 403);
    }

    // Validation CSRF
    $csrf_token = $request->request->get('csrf_token');
    $token_service = \Drupal::service('csrf_token');
    if (!$csrf_token || !$token_service->validate($csrf_token, '/client/cloture-projet')) {
      return new JsonResponse(['success' => false, 'message' => $this->t('Token CSRF invalide.')], 403);
    }

    $code = (string) $request->request->get('code', '');
    $rating = (int) $request->request->get('rating', 0);
    $comment = (string) $request->request->get('comment', '');

    if (!$devis_id || !$code) {
      return new JsonResponse(['success' => false, 'message' => $this->t('ID de devis ou code manquant.')], 400);
    }

    $devis = Node::load($devis_id);
    if (!$devis || $devis->bundle() !== 'devis') {
      return new JsonResponse(['success' => false, 'message' => $this->t('Devis introuvable.')], 404);
    }

    // Le client qui clôture doit être le propriétaire de la demande liée au devis.
    $demande = $devis->get('field_demande_ref')->entity ?? NULL;
    if (!$demande || (int) $demande->getOwnerId() !== (int) $this->currentUser->id()) {
      return new JsonResponse(['success' => false, 'message' => $this->t('Accès refusé.')], 403);
    }

    // Vérifier le code OTP
    $kv = \Drupal::keyValue('client_demandes_otp');
    $otp_data = $kv->get("devis:$devis_id:otp");

    if (!$otp_data) {
      return new JsonResponse(['success' => false, 'message' => $this->t('Code de validation non trouvé ou expiré.')], 400);
    }

    // Vérifier l'expiration
    if (\Drupal::time()->getRequestTime() > $otp_data['expires']) {
      $kv->delete("devis:$devis_id:otp");
      return new JsonResponse(['success' => false, 'message' => $this->t('Code de validation expiré.')], 400);
    }

    // Vérifier le code
    if ($code !== $otp_data['code']) {
      // Décrémenter les tentatives
      $otp_data['attempts']--;
      if ($otp_data['attempts'] <= 0) {
        $kv->delete("devis:$devis_id:otp");
        return new JsonResponse(['success' => false, 'message' => $this->t('Trop de tentatives. Code invalidé.')], 400);
      }
      $kv->set("devis:$devis_id:otp", $otp_data);
      return new JsonResponse(['success' => false, 'message' => $this->t('Code incorrect. @attempts tentatives restantes.', ['@attempts' => $otp_data['attempts']])], 400);
    }

    // Code valide, procéder à la clôture
    $kv->delete("devis:$devis_id:otp");

    // Enregistrer rating/comment si disponibles.
    if ($devis->hasField('field_rating') && $rating > 0) {
      $devis->set('field_rating', $rating);
    }
    if ($devis->hasField('field_review') && $comment !== '') {
      $devis->set('field_review', $comment);
    }

    // Marquer comme clôturé
    $devis->set('field_status', 'closed');
    $devis->save();

    // Envoyer les notifications
    $cloture_data = [
      'rating' => $rating,
      'comment' => $comment,
      'closure_method' => 'code_validation'
    ];
    $this->sendClotureNotifications($devis, $demande, $cloture_data);

    return new JsonResponse([
      'success' => true,
      'message' => $this->t('Projet clôturé avec succès.')
    ]);
  }





  /**
   * Envoie les notifications de clôture au client et au prestataire.
   */
  protected function sendClotureNotifications($devis, $demande, $cloture_data)
  {
    try {
      $email_service = \Drupal::service('client_demandes.email_notification_service');

      // Email au client
      $email_service->sendProjectClosureClientNotification($devis, $demande, $cloture_data);

      // Email au prestataire avec notification de paiement
      $email_service->sendProjectClosurePrestataireNotification($devis, $demande, $cloture_data);

      \Drupal::logger('client_demandes')->info(
        'Notifications de clôture envoyées pour le devis @devis_id',
        ['@devis_id' => $devis->id()]
      );
    } catch (\Exception $e) {
      \Drupal::logger('client_demandes')->warning(
        'Impossible d\'envoyer les notifications de clôture: @message',
        ['@message' => $e->getMessage()]
      );
    }
  }

  /**
   * Vérifie le statut des commandes Commerce pour un devis.
   */
  protected function checkOrderStatus($devis_node)
  {
    try {
      // Rechercher les commandes liées à ce devis
      $order_query = \Drupal::entityQuery('commerce_order')
        ->accessCheck(FALSE)
        ->condition('field_devis_ref', $devis_node->id())
        ->condition('state', 'completed')
        ->sort('created', 'DESC')
        ->range(0, 1);

      $order_ids = $order_query->execute();

      if (!empty($order_ids)) {
        $order_id = reset($order_ids);
        $order = \Drupal\commerce_order\Entity\Order::load($order_id);

        if ($order && $order->getState()->getId() === 'completed') {
          return 'completed';
        }
      }

      // Si pas de commande completed, vérifier s'il y a des commandes en cours
      $pending_order_query = \Drupal::entityQuery('commerce_order')
        ->accessCheck(FALSE)
        ->condition('field_devis_ref', $devis_node->id())
        ->condition('state', ['draft', 'pending'], 'IN')
        ->range(0, 1);

      $pending_order_ids = $pending_order_query->execute();

      if (!empty($pending_order_ids)) {
        return 'pending';
      }

      return 'none';
    } catch (\Exception $e) {
      \Drupal::logger('client_demandes')->error(
        'Erreur lors de la vérification du statut des commandes pour le devis @devis_id: @message',
        [
          '@devis_id' => $devis_node->id(),
          '@message' => $e->getMessage(),
        ]
      );
      return 'error';
    }
  }
  public function facturePDF($devis_id): Response
  {
    $devis = Node::load($devis_id);
    if (!$devis || $devis->bundle() !== 'devis') {
      return new Response('Devis introuvable', 404);
    }

    // Le client est celui qui a créé la demande liée au devis
    $demande_ref = $devis->get('field_demande_ref')->entity;
    $client = $demande_ref ? $demande_ref->getOwner() : null;

    // Le prestataire est le propriétaire du devis
    $prestataire = $devis->getOwner();

    $html = '<h1>Facture du devis #' . $devis_id . '</h1>';
    $html .= '<p>Client : ' . $client->getDisplayName() . '</p>';
    $html .= '<p>Prestataire : ' . $prestataire->getDisplayName() . '</p>';
    $html .= '<p>Montant total : ' . number_format((float) $devis->get('field_price')->value ?? 0, 2) . ' €</p>';
    $html .= '<p>Montant payé : ' . number_format($devis->get('field_paid_amount')->value, 2) . ' €</p>';
    $html .= '<p>Statut : ' . $devis->get('field_status')->value . '</p>';
    $html .= '<p>Date : ' . date('d/m/Y') . '</p>';

    $options = new Options();
    $options->set('defaultFont', 'DejaVu Sans');
    $dompdf = new Dompdf($options);
    $dompdf->loadHtml($html);
    $dompdf->setPaper('A4', 'portrait');
    $dompdf->render();

    $file_path = 'public://facture_devis_' . $devis_id . '.pdf';
    file_put_contents($file_path, $dompdf->output());

    $mailManager = \Drupal::service('plugin.manager.mail');
    $module = 'client_demandes';
    $key = 'facture_pdf';
    $to = $client->getEmail();
    $params['subject'] = 'Votre facture - Devis #' . $devis_id;
    $params['body'] = 'Merci pour votre paiement. Vous trouverez votre facture en pièce jointe.';
    $params['attachments'][] = [
      'filepath' => $file_path,
      'filename' => 'facture_devis_' . $devis_id . '.pdf',
      'filemime' => 'application/pdf',
    ];
    $langcode = $client->getPreferredLangcode();
    $send = true;
    $mailManager->mail($module, $key, $to, $langcode, $params, NULL, $send);

    return new Response(
      $dompdf->output(),
      200,
      [
        'Content-Type' => 'application/pdf',
        'Content-Disposition' => 'inline; filename="facture_devis_' . $devis_id . '.pdf"',
      ]
    );
  }
  /**
   * Action "Libérer les fonds"
   */
  public function libererFonds($devis_id)
  {
    $current_user = $this->currentUser();

    $devis = Node::load($devis_id);
    if (!$devis || $devis->bundle() !== 'devis') {
      $this->messenger()->addError("Devis introuvable.");
      return new RedirectResponse('/mon-espace/mes-demandes');
    }

    if (!$devis->hasField('field_paid')) {
      \Drupal::logger('client_demandes')->error('Champ field_paid manquant sur le contenu devis.');
      $this->messenger()->addError("Erreur technique : champ de paiement manquant.");
      return new RedirectResponse('/mon-espace/mes-demandes');
    }

    // Vérifie que l’utilisateur connecté est bien le client ayant créé la demande liée
    $demande = $devis->get('field_demande_ref')->entity;
    if (!$demande || $demande->getOwnerId() != $current_user->id()) {
      $this->messenger()->addError("Vous n'avez pas l'autorisation de libérer les fonds pour ce devis.");
      return new RedirectResponse('/mon-espace/mes-demandes');
    }

    // Vérifie que le statut est bien 'depot'
    if ($devis->get('field_status')->value !== 'depot') {
      $request = \Drupal::request();
      if ($request->isXmlHttpRequest()) {
        return new JsonResponse(['error' => 'Les fonds ne peuvent pas être libérés pour ce devis.'], 400);
      }
      $this->messenger()->addWarning("Les fonds ne peuvent pas encore être libérés.");
      return new RedirectResponse('/client/dashboard');
    }

    // Récupérer le prestataire
    $prestataire = $devis->getOwner();

    try {
      // Pour l'instant, simuler la libération des fonds
      // TODO: Implémenter la vraie logique Stripe Connect quand les comptes seront configurés

      // Mise à jour du statut du devis
      $devis->set('field_status', 'paid');
      $devis->save();

      // Log de l'action
      \Drupal::logger('client_demandes')->info(
        'Fonds libérés (simulation) pour le devis @devis_id par le client @client_id vers le prestataire @prestataire_id',
        [
          '@devis_id' => $devis->id(),
          '@client_id' => $current_user->id(),
          '@prestataire_id' => $prestataire->id(),
        ]
      );

      $request = \Drupal::request();
      if ($request->isXmlHttpRequest()) {
        return new JsonResponse([
          'success' => true,
          'message' => 'Les fonds ont été libérés avec succès.',
          'transfer_id' => 'SIMULATION_' . time(),
        ]);
      }

      $this->messenger()->addStatus("✅ Les fonds ont été libérés au prestataire.");
    } catch (\Exception $e) {
      \Drupal::logger('client_demandes')->error(
        'Erreur lors de la libération des fonds pour le devis @devis_id : @msg',
        ['@devis_id' => $devis->id(), '@msg' => $e->getMessage()]
      );

      $request = \Drupal::request();
      if ($request->isXmlHttpRequest()) {
        return new JsonResponse(['error' => 'Une erreur est survenue lors du transfert.'], 500);
      }

      $this->messenger()->addError("Une erreur est survenue lors du transfert.");
    }

    return new RedirectResponse('/client/dashboard');
  }

  /**
   * Prépare les options de filtres pour les devis.
   */
  private function prepareFilterOptions($devis)
  {
    $filter_options = [
      'status' => [],
      'dates' => [],
      'prestataires' => [],
      'prix_ranges' => [
        '0-500' => '0€ - 500€',
        '500-1000' => '500€ - 1000€',
        '1000-2000' => '1000€ - 2000€',
        '2000-5000' => '2000€ - 5000€',
        '5000+' => '5000€+',
      ],
    ];

    // Collecter les statuts uniques
    $status_labels = [
      'pending' => 'En attente',
      'accepted' => 'Accepté',
      'rejected' => 'Refusé',
      'paid' => 'Payé',
      'partial_paid' => 'Paiement partiel',
      'depot' => 'Dépôt',
      'closed' => 'Clôturé',
      'ready_to_close' => 'Prêt à clôturer',
    ];

    $status_counts = [];
    $prestataire_counts = [];
    $date_ranges = [];

    foreach ($devis as $devis_item) {
      // Compter les statuts
      $status = 'pending'; // Valeur par défaut
      if (isset($devis_item->field_status) && !$devis_item->field_status->isEmpty()) {
        $status = $devis_item->field_status->value;
      }
      $status_counts[$status] = ($status_counts[$status] ?? 0) + 1;

      // Compter les prestataires
      $prestataire_name = 'Inconnu'; // Valeur par défaut
      if (isset($devis_item->uid) && $devis_item->uid->entity) {
        $prestataire_name = $devis_item->uid->entity->getDisplayName();
      }
      $prestataire_counts[$prestataire_name] = ($prestataire_counts[$prestataire_name] ?? 0) + 1;

      // Analyser les dates
      if (isset($devis_item->created) && !$devis_item->created->isEmpty()) {
        $date = $devis_item->created->value;
        $month_year = date('Y-m', $date);
        $date_ranges[$month_year] = ($date_ranges[$month_year] ?? 0) + 1;
      }
    }

    // Préparer les options de statut avec compteurs
    foreach ($status_counts as $status => $count) {
      $label = $status_labels[$status] ?? ucfirst($status);
      $filter_options['status'][$status] = [
        'label' => $label,
        'count' => $count,
        'class' => $this->getStatusClass($status),
      ];
    }

    // Préparer les options de prestataires avec compteurs
    foreach ($prestataire_counts as $prestataire => $count) {
      $filter_options['prestataires'][$prestataire] = [
        'label' => $prestataire,
        'count' => $count,
      ];
    }

    // Préparer les options de dates avec compteurs
    krsort($date_ranges); // Trier par date décroissante
    foreach ($date_ranges as $month_year => $count) {
      $date_label = date('F Y', strtotime($month_year . '-01'));
      $filter_options['dates'][$month_year] = [
        'label' => $date_label,
        'count' => $count,
      ];
    }

    return $filter_options;
  }

  /**
   * Retourne la classe CSS pour un statut donné.
   */
  private function getStatusClass($status)
  {
    $status_classes = [
      'pending' => 'warning',
      'accepted' => 'success',
      'rejected' => 'danger',
      'paid' => 'primary',
      'partial_paid' => 'info',
      'depot' => 'warning',
      'closed' => 'secondary',
      'ready_to_close' => 'success',
    ];

    return $status_classes[$status] ?? 'secondary';
  }
}
