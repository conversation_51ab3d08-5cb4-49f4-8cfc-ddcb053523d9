/**
 * @file
 * JavaScript pour les dashboards client et prestataire.
 */

(function ($, Drupal, once) {
  'use strict';

  /**
   * Comportement pour les dashboards.
   */
  Drupal.behaviors.clientDemandesDashboard = {
    attach: function (context, settings) {
      // Initialiser les fonctionnalités du dashboard
      this.initDevisCards(context);
      this.initStatistics(context);
      this.initActions(context);
      this.initFilters(context);
    },

    /**
     * Initialise les cartes de devis.
     */
    initDevisCards: function (context) {
      $(once('devis-card', '.devis-card', context)).each(function () {
        const $card = $(this);

        // Effet hover amélioré
        $card.hover(
          function () {
            $(this).addClass('shadow-lg');
          },
          function () {
            $(this).removeClass('shadow-lg');
          }
        );

        // Animation d'entrée
        $card.css('opacity', '0').animate({
          opacity: 1
        }, 300 + Math.random() * 200);
      });

      // Cartes de demandes (pour prestataires)
      $(once('demande-card', '.demande-card', context)).each(function () {
        const $card = $(this);

        $card.hover(
          function () {
            $(this).find('.btn').addClass('btn-pulse');
          },
          function () {
            $(this).find('.btn').removeClass('btn-pulse');
          }
        );
      });
    },

    /**
     * Initialise les statistiques animées.
     */
    initStatistics: function (context) {
      $(once('stat-animation', '.stat-card', context)).each(function () {
        const $card = $(this);
        const $number = $card.find('h3');
        const finalValue = parseInt($number.text()) || 0;

        // Animation du compteur
        if (finalValue > 0) {
          $number.text('0');
          $({ counter: 0 }).animate({ counter: finalValue }, {
            duration: 1500,
            easing: 'swing',
            step: function () {
              $number.text(Math.ceil(this.counter));
            },
            complete: function () {
              $number.text(finalValue);
            }
          });
        }

        // Effet hover sur les cartes de stats
        $card.hover(
          function () {
            $(this).find('.stat-icon').addClass('fa-bounce');
          },
          function () {
            $(this).find('.stat-icon').removeClass('fa-bounce');
          }
        );
      });
    },

    /**
     * Initialise les actions sur les devis.
     */
    initActions: function (context) {
      // Boutons d'acceptation
      $(once('accept-action', '.devis-accept-btn, .btn-success[href*="accepter"]', context)).on('click', function (e) {
        const $btn = $(this);

        // Ajouter un effet de chargement
        $btn.prop('disabled', true);
        $btn.html('<i class="fas fa-spinner fa-spin me-1"></i>' + Drupal.t('Traitement...'));

        // Permettre la navigation après un court délai
        setTimeout(() => {
          window.location.href = $btn.attr('href');
        }, 500);

        e.preventDefault();
      });

      // Boutons de refus avec confirmation
      $(once('refuse-action', '.devis-refuse-btn, .btn-danger[href*="refuser"]', context)).on('click', function (e) {
        e.preventDefault();

        const $btn = $(this);
        const href = $btn.attr('href');

        // Modal de confirmation personnalisée
        const modal = $('<div class="modal fade" tabindex="-1">' +
          '<div class="modal-dialog">' +
            '<div class="modal-content">' +
              '<div class="modal-header">' +
                '<h5 class="modal-title">' + Drupal.t('Confirmer le refus') + '</h5>' +
                '<button type="button" class="btn-close" data-bs-dismiss="modal"></button>' +
              '</div>' +
              '<div class="modal-body">' +
                '<p>' + Drupal.t('Êtes-vous sûr de vouloir refuser ce devis ?') + '</p>' +
                '<p class="text-muted small">' + Drupal.t('Cette action ne peut pas être annulée.') + '</p>' +
              '</div>' +
              '<div class="modal-footer">' +
                '<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">' + Drupal.t('Annuler') + '</button>' +
                '<button type="button" class="btn btn-danger confirm-refuse">' + Drupal.t('Refuser le devis') + '</button>' +
              '</div>' +
            '</div>' +
          '</div>' +
        '</div>');

        $('body').append(modal);
        modal.modal('show');

        // Gérer la confirmation
        modal.find('.confirm-refuse').on('click', function () {
          window.location.href = href;
        });

        // Nettoyer le modal après fermeture
        modal.on('hidden.bs.modal', function () {
          modal.remove();
        });
      });

      // Bouton "Clôturer le projet" - géré par le JavaScript inline dans le template

      // Bouton "Libérer les fonds" - géré par le JavaScript inline dans le template

      // Boutons de clôture de projet
      $(once('cloture-action', '.btn-cloture-projet', context)).on('click', function (e) {
        e.preventDefault();
        const $btn = $(this);
        const devisId = $btn.data('devis-id');
        const csrfToken = $btn.data('csrf-token');

        // Stocker les données pour la modal
        $('#modalCloture').data('devis-id', devisId);
        $('#modalCloture').data('csrf-token', csrfToken);

        // Afficher la modal
        const modal = new bootstrap.Modal(document.getElementById('modalCloture'));
        modal.show();
      });

      // Boutons de libération des fonds
      $(once('liberation-action', '.btn-liberer-fonds', context)).on('click', function (e) {
        e.preventDefault();
        const $btn = $(this);
        const devisId = $btn.data('devis-id');
        const csrfToken = $btn.data('csrf-token');

        // Stocker les données pour la modal
        $('#modalLiberationFonds').data('devis-id', devisId);
        $('#modalLiberationFonds').data('csrf-token', csrfToken);

        // Afficher la modal
        const modal = new bootstrap.Modal(document.getElementById('modalLiberationFonds'));
        modal.show();
      });

      // Boutons de clôture avec code
      $(once('cloture-code-action', '.btn-cloture-avec-code', context)).on('click', function (e) {
        e.preventDefault();
        const $btn = $(this);
        const devisId = $btn.data('devis-id');
        const csrfToken = $btn.data('csrf-token');

        // Stocker les données pour la modal
        $('#modalClotureAvecCode').data('devis-id', devisId);
        $('#modalClotureAvecCode').data('csrf-token', csrfToken);

        // Reset du formulaire
        $('#formClotureAvecCode')[0].reset();

        // Afficher la modal
        const modal = new bootstrap.Modal(document.getElementById('modalClotureAvecCode'));
        modal.show();

        // Focus sur le champ de code après l'ouverture de la modal
        $('#modalClotureAvecCode').on('shown.bs.modal', function () {
          $('#codeValidation').focus();
        });
      });

      // Confirmation de clôture de projet
      $(once('confirm-cloture', '#btnConfirmCloture', context)).on('click', function () {
        const $modal = $('#modalCloture');
        const devisId = $modal.data('devis-id');
        const csrfToken = $modal.data('csrf-token');
        const rating = $('input[name="rating"]:checked').val();
        const comment = $('#comment').val();

        if (!rating) {
          alert(Drupal.t('Veuillez donner une note au prestataire.'));
          return;
        }

        // Désactiver le bouton
        $(this).prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-1"></i>' + Drupal.t('Traitement...'));

        // Envoyer la requête AJAX
        $.ajax({
          url: '/client/devis/' + devisId + '/cloturer',
          method: 'POST',
          data: {
            rating: rating,
            comment: comment,
            _token: csrfToken
          },
          success: function (response) {
            if (response.success) {
              // Fermer la modal
              const modal = bootstrap.Modal.getInstance(document.getElementById('modalCloture'));
              modal.hide();

              // Afficher un message de succès
              Drupal.behaviors.clientDemandesNotifications.showToast(
                Drupal.t('Projet clôturé avec succès'),
                'success'
              );

              // Recharger la page après un délai
              setTimeout(() => {
                window.location.reload();
              }, 1500);
            } else {
              alert(response.message || Drupal.t('Erreur lors de la clôture du projet.'));
            }
          },
          error: function () {
            alert(Drupal.t('Erreur de communication avec le serveur.'));
          },
          complete: function () {
            $('#btnConfirmCloture').prop('disabled', false).html(Drupal.t('Clôturer le projet'));
          }
        });
      });

      // Confirmation de libération des fonds
      $(once('confirm-liberation', '#btnConfirmLiberationFonds', context)).on('click', function () {
        const $modal = $('#modalLiberationFonds');
        const devisId = $modal.data('devis-id');
        const csrfToken = $modal.data('csrf-token');

        // Désactiver le bouton
        $(this).prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-1"></i>' + Drupal.t('Traitement...'));

        // Envoyer la requête AJAX
        $.ajax({
          url: '/client/devis/' + devisId + '/liberer-fonds',
          method: 'POST',
          data: {
            _token: csrfToken
          },
          success: function (response) {
            if (response.success) {
              // Fermer la modal
              const modal = bootstrap.Modal.getInstance(document.getElementById('modalLiberationFonds'));
              modal.hide();

              // Afficher un message de succès
              Drupal.behaviors.clientDemandesNotifications.showToast(
                Drupal.t('Fonds libérés avec succès'),
                'success'
              );

              // Recharger la page après un délai
              setTimeout(() => {
                window.location.reload();
              }, 1500);
            } else {
              alert(response.message || Drupal.t('Erreur lors de la libération des fonds.'));
            }
          },
          error: function () {
            alert(Drupal.t('Erreur de communication avec le serveur.'));
          },
          complete: function () {
            $('#btnConfirmLiberationFonds').prop('disabled', false).html(Drupal.t('Libérer les fonds'));
          }
        });
      });

      // Confirmation de clôture avec code
      $(once('confirm-cloture-code', '#btnConfirmClotureAvecCode', context)).on('click', function () {
        const $modal = $('#modalClotureAvecCode');
        const devisId = $modal.data('devis-id');
        const csrfToken = $modal.data('csrf-token');
        const code = $('#codeValidation').val();
        const rating = $('#formClotureAvecCode input[name="rating"]:checked').val();
        const comment = $('#commentAvecCode').val();

        // Validation du code
        if (!code || code.length !== 6 || !/^\d{6}$/.test(code)) {
          alert(Drupal.t('Veuillez saisir un code de validation valide (6 chiffres).'));
          $('#codeValidation').focus();
          return;
        }

        if (!rating) {
          alert(Drupal.t('Veuillez donner une note au prestataire.'));
          return;
        }

        // Désactiver le bouton
        $(this).prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-1"></i>' + Drupal.t('Validation en cours...'));

        // Envoyer la requête AJAX
        $.ajax({
          url: '/client/devis/' + devisId + '/cloturer-avec-code',
          method: 'POST',
          data: {
            code: code,
            rating: rating,
            comment: comment,
            csrf_token: csrfToken
          },
          success: function (response) {
            if (response.success) {
              // Fermer la modal
              const modal = bootstrap.Modal.getInstance(document.getElementById('modalClotureAvecCode'));
              modal.hide();

              // Afficher un message de succès
              Drupal.behaviors.clientDemandesNotifications.showToast(
                Drupal.t('Projet clôturé avec succès grâce au code de validation !'),
                'success'
              );

              // Recharger la page après un délai
              setTimeout(() => {
                window.location.reload();
              }, 1500);
            } else {
              alert(response.message || Drupal.t('Code de validation incorrect.'));
              $('#codeValidation').focus().select();
            }
          },
          error: function (xhr) {
            let errorMessage = Drupal.t('Erreur lors de la validation du code.');
            if (xhr.responseJSON && xhr.responseJSON.message) {
              errorMessage = xhr.responseJSON.message;
            }
            alert(errorMessage);
            $('#codeValidation').focus().select();
          },
          complete: function () {
            $('#btnConfirmClotureAvecCode').prop('disabled', false).html(Drupal.t('Valider et clôturer'));
          }
        });
      });

      // Amélioration UX pour le champ de code de validation
      $(once('code-validation-ux', '#codeValidation', context)).on('input', function () {
        let value = $(this).val();

        // Supprimer tous les caractères non numériques
        value = value.replace(/\D/g, '');

        // Limiter à 6 chiffres
        if (value.length > 6) {
          value = value.substring(0, 6);
        }

        // Mettre à jour la valeur
        $(this).val(value);

        // Validation visuelle
        if (value.length === 6) {
          $(this).removeClass('is-invalid').addClass('is-valid');
        } else if (value.length > 0) {
          $(this).removeClass('is-valid').addClass('is-invalid');
        } else {
          $(this).removeClass('is-valid is-invalid');
        }
      });

      // Validation en temps réel du formulaire de clôture avec code
      $(once('form-validation-code', '#formClotureAvecCode', context)).on('input change', function () {
        const code = $('#codeValidation').val();
        const rating = $('#formClotureAvecCode input[name="rating"]:checked').val();
        const isValid = code.length === 6 && /^\d{6}$/.test(code) && rating;

        $('#btnConfirmClotureAvecCode').prop('disabled', !isValid);
      });

      // Permettre la soumission avec Entrée
      $(once('code-enter-submit', '#codeValidation', context)).on('keypress', function (e) {
        if (e.which === 13) { // Touche Entrée
          e.preventDefault();
          if (!$('#btnConfirmClotureAvecCode').prop('disabled')) {
            $('#btnConfirmClotureAvecCode').click();
          }
        }
      });

    },

    /**
     * Initialise les filtres et la recherche.
     */
    initFilters: function (context) {
      // Filtre par statut
      if ($('.status-filter', context).length) {
        $(once('status-filter', '.status-filter', context)).on('change', function () {
          const selectedStatus = $(this).val();

          $('.devis-card, .demande-card').each(function () {
            const $card = $(this);
            const cardStatus = $card.data('status') || $card.find('.badge').text().toLowerCase();

            if (selectedStatus === '' || cardStatus.includes(selectedStatus)) {
              $card.fadeIn();
            } else {
              $card.fadeOut();
            }
          });
        });
      }

      // Recherche en temps réel
      if ($('.search-input', context).length) {
        $('.search-input', context).once('search-filter').on('input', function () {
          const searchTerm = $(this).val().toLowerCase();

          $('.devis-card, .demande-card').each(function () {
            const $card = $(this);
            const cardText = $card.text().toLowerCase();

            if (searchTerm === '' || cardText.includes(searchTerm)) {
              $card.fadeIn();
            } else {
              $card.fadeOut();
            }
          });
        });
      }
    }
  };

  /**
   * Utilitaires pour les dashboards.
   */
  Drupal.clientDemandesDashboard = {

    /**
     * Actualise une section du dashboard.
     */
    refreshSection: function (sectionSelector) {
      const $section = $(sectionSelector);
      $section.addClass('loading');

      // Simuler un rechargement
      setTimeout(() => {
        $section.removeClass('loading');
        Drupal.clientDemandesPaiement.showNotification(
          Drupal.t('Section actualisée'),
          'success'
        );
      }, 1000);
    },

    /**
     * Affiche/masque les détails d'une carte.
     */
    toggleCardDetails: function (cardSelector) {
      const $card = $(cardSelector);
      const $details = $card.find('.card-details');

      if ($details.is(':visible')) {
        $details.slideUp();
        $card.find('.toggle-btn').html('<i class="fas fa-chevron-down"></i>');
      } else {
        $details.slideDown();
        $card.find('.toggle-btn').html('<i class="fas fa-chevron-up"></i>');
      }
    },

    /**
     * Exporte les données du dashboard.
     */
    exportData: function (format = 'csv') {
      // Placeholder pour l'export de données
      Drupal.clientDemandesPaiement.showNotification(
        Drupal.t('Export en cours...'),
        'info'
      );

      // Ici on pourrait implémenter un vrai export
      setTimeout(() => {
        Drupal.clientDemandesPaiement.showNotification(
          Drupal.t('Export terminé'),
          'success'
        );
      }, 2000);
    }
  };

  /**
   * Comportement pour l'auto-completion du titre de devis.
   */
  Drupal.behaviors.devisAutoTitle = {
    attach: function (context, settings) {
      // Auto-completion du titre basé sur la demande sélectionnée
      $('#edit-field-demande-0-target-id', context).once('devis-auto-title').on('autocompleteclose', function() {
        var demandeTitle = $(this).val();
        if (demandeTitle) {
          // Extraire le titre de la demande (format: "Titre (ID)")
          var match = demandeTitle.match(/^(.+)\s\(\d+\)$/);
          if (match) {
            var cleanTitle = match[1];
            var autoTitle = 'Réponse : ' + cleanTitle;
            $('#edit-title-0-value').val(autoTitle);
          }
        }
      });

      // Pour le formulaire personnalisé DevisForm
      $('#edit-field-demande-ref', context).once('devis-auto-title-custom').on('autocompleteclose', function() {
        var demandeTitle = $(this).val();
        if (demandeTitle) {
          var match = demandeTitle.match(/^(.+)\s\(\d+\)$/);
          if (match) {
            var cleanTitle = match[1];
            var autoTitle = 'Réponse : ' + cleanTitle;
            $('#edit-title').val(autoTitle);
          }
        }
      });

      // Validation en temps réel pour le formulaire de devis
      $('.devis-form input[required], .devis-form textarea[required]', context).once('devis-validation').on('blur', function() {
        var $field = $(this);
        var value = $field.val().trim();

        if (value === '') {
          $field.addClass('is-invalid').removeClass('is-valid');
        } else {
          $field.removeClass('is-invalid').addClass('is-valid');
        }
      });

      // Animation du formulaire de devis
      $('.devis-form .form-group', context).once('devis-animation').each(function(index) {
        var $group = $(this);
        $group.css({
          'opacity': '0',
          'transform': 'translateY(20px)',
          'transition': 'all 0.5s ease'
        });

        setTimeout(function() {
          $group.css({
            'opacity': '1',
            'transform': 'translateY(0)'
          });
        }, index * 100);
      });
    }
  };

  /**
   * Comportement pour la gestion Stripe des prestataires.
   */
  Drupal.behaviors.prestataireStripe = {
    attach: function (context, settings) {
      // Gestion des boutons Stripe
      $(once('stripe-create', '#createStripeAccountBtn', context)).on('click', function() {
        createStripeAccount();
      });

      $(once('stripe-refresh', '#refreshStripeStatusBtn', context)).on('click', function() {
        refreshStripeStatus();
      });

      $(once('stripe-disconnect', '#confirmDisconnectBtn', context)).on('click', function() {
        disconnectStripe();
      });
    }
  };

  /**
   * Fonctions utilitaires pour les notifications.
   */

  /**
   * Affiche un message de succès.
   */
  window.showSuccessMessage = function(message) {
    showNotification(message, 'success');
  };

  /**
   * Affiche un message d'erreur.
   */
  window.showErrorMessage = function(message) {
    showNotification(message, 'error');
  };

  /**
   * Affiche une notification générique.
   */
  window.showNotification = function(message, type = 'info') {
    // Créer le conteneur de notifications s'il n'existe pas
    let container = document.getElementById('notification-container');
    if (!container) {
      container = document.createElement('div');
      container.id = 'notification-container';
      container.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 9999;
        max-width: 400px;
      `;
      document.body.appendChild(container);
    }

    // Créer la notification
    const notification = document.createElement('div');
    const alertClass = type === 'success' ? 'alert-success' :
                      type === 'error' ? 'alert-danger' :
                      type === 'warning' ? 'alert-warning' : 'alert-info';

    const icon = type === 'success' ? 'check-circle' :
                 type === 'error' ? 'exclamation-circle' :
                 type === 'warning' ? 'exclamation-triangle' : 'info-circle';

    notification.className = `alert ${alertClass} alert-dismissible fade show mb-2`;
    notification.style.cssText = `
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      border: none;
      animation: slideInRight 0.3s ease-out;
    `;

    notification.innerHTML = `
      <div class="d-flex align-items-center">
        <i class="fas fa-${icon} me-2"></i>
        <span>${message}</span>
        <button type="button" class="btn-close ms-auto" data-bs-dismiss="alert"></button>
      </div>
    `;

    // Ajouter l'animation CSS si elle n'existe pas
    if (!document.getElementById('notification-styles')) {
      const style = document.createElement('style');
      style.id = 'notification-styles';
      style.textContent = `
        @keyframes slideInRight {
          from {
            transform: translateX(100%);
            opacity: 0;
          }
          to {
            transform: translateX(0);
            opacity: 1;
          }
        }
        @keyframes slideOutRight {
          from {
            transform: translateX(0);
            opacity: 1;
          }
          to {
            transform: translateX(100%);
            opacity: 0;
          }
        }
      `;
      document.head.appendChild(style);
    }

    // Ajouter la notification au conteneur
    container.appendChild(notification);

    // Auto-suppression après 5 secondes
    setTimeout(() => {
      if (notification.parentNode) {
        notification.style.animation = 'slideOutRight 0.3s ease-in';
        setTimeout(() => {
          if (notification.parentNode) {
            notification.remove();
          }
        }, 300);
      }
    }, 5000);

    // Gestion du bouton de fermeture
    const closeBtn = notification.querySelector('.btn-close');
    if (closeBtn) {
      closeBtn.addEventListener('click', () => {
        notification.style.animation = 'slideOutRight 0.3s ease-in';
        setTimeout(() => {
          if (notification.parentNode) {
            notification.remove();
          }
        }, 300);
      });
    }
  };

})(jQuery, Drupal);

// Fonctions globales pour la gestion Stripe
function createStripeAccount() {
  const btn = document.querySelector('[onclick="createStripeAccount()"]');
  if (btn) {
    btn.disabled = true;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>' + Drupal.t('Création en cours...');
  }

  fetch('/prestataire/stripe/onboarding/create', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'X-Requested-With': 'XMLHttpRequest'
    }
  })
  .then(response => {
    // Vérifier si la réponse est du JSON
    const contentType = response.headers.get('content-type');
    if (contentType && contentType.includes('application/json')) {
      return response.json();
    } else {
      // Si ce n'est pas du JSON, c'est probablement une erreur HTML
      return response.text().then(text => {
        throw new Error('Réponse non-JSON reçue: ' + text.substring(0, 100) + '...');
      });
    }
  })
  .then(data => {
    if (data.success && data.onboarding_url) {
      showSuccessMessage(data.message);
      // Redirection vers Stripe après un court délai
      setTimeout(() => {
        window.location.href = data.onboarding_url;
      }, 1000);
    } else {
      // Gestion des erreurs spécifiques
      if (data.error_type === 'stripe_connect_not_enabled') {
        showErrorMessage(data.message + ' ' + Drupal.t('Consultez la section d\'aide ci-dessous.'));
        // Faire défiler vers la section d'aide
        setTimeout(() => {
          const helpSection = document.querySelector('.stripe-setup-help');
          if (helpSection) {
            helpSection.scrollIntoView({ behavior: 'smooth' });
          }
        }, 2000);
      } else if (data.error_type === 'stripe_config_missing') {
        showErrorMessage(data.message);
      } else {
        showErrorMessage(data.message || Drupal.t('Erreur lors de la création du compte.'));
      }
    }
  })
  .catch(error => {
    console.error('Erreur:', error);
    showErrorMessage(Drupal.t('Une erreur technique est survenue.'));
  })
  .finally(() => {
    if (btn) {
      btn.disabled = false;
      btn.innerHTML = '<i class="fas fa-plus me-2"></i>' + Drupal.t('Créer mon compte Stripe');
    }
  });
}

function refreshStripeStatus() {
  const btn = document.querySelector('[onclick="refreshStripeStatus()"]');
  if (btn) {
    btn.disabled = true;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>' + Drupal.t('Actualisation...');
  }

  fetch('/prestataire/stripe/refresh-status', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'X-Requested-With': 'XMLHttpRequest'
    }
  })
  .then(response => {
    const contentType = response.headers.get('content-type');
    if (contentType && contentType.includes('application/json')) {
      return response.json();
    } else {
      return response.text().then(text => {
        throw new Error('Réponse non-JSON reçue: ' + text.substring(0, 100) + '...');
      });
    }
  })
  .then(data => {
    if (data.success) {
      showSuccessMessage(data.message);
      // Recharger la page pour afficher le nouveau statut
      setTimeout(() => {
        window.location.reload();
      }, 1000);
    } else {
      showErrorMessage(data.message || Drupal.t('Erreur lors de la mise à jour.'));
    }
  })
  .catch(error => {
    console.error('Erreur:', error);
    showErrorMessage(Drupal.t('Une erreur technique est survenue.'));
  })
  .finally(() => {
    if (btn) {
      btn.disabled = false;
      btn.innerHTML = '<i class="fas fa-sync-alt me-2"></i>' + Drupal.t('Actualiser le statut');
    }
  });
}

function confirmDisconnectStripe() {
  const modal = new bootstrap.Modal(document.getElementById('disconnectModal'));
  modal.show();
}

function disconnectStripe() {
  const btn = document.getElementById('confirmDisconnectBtn');
  if (btn) {
    btn.disabled = true;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>' + Drupal.t('Déconnexion...');
  }

  fetch('/prestataire/stripe/disconnect', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'X-Requested-With': 'XMLHttpRequest'
    }
  })
  .then(response => {
    const contentType = response.headers.get('content-type');
    if (contentType && contentType.includes('application/json')) {
      return response.json();
    } else {
      return response.text().then(text => {
        throw new Error('Réponse non-JSON reçue: ' + text.substring(0, 100) + '...');
      });
    }
  })
  .then(data => {
    if (data.success) {
      showSuccessMessage(data.message);
      // Fermer la modal et recharger la page
      const modal = bootstrap.Modal.getInstance(document.getElementById('disconnectModal'));
      modal.hide();
      setTimeout(() => {
        window.location.reload();
      }, 1000);
    } else {
      showErrorMessage(data.message || Drupal.t('Erreur lors de la déconnexion.'));
    }
  })
  .catch(error => {
    console.error('Erreur:', error);
    showErrorMessage(Drupal.t('Une erreur technique est survenue.'));
  })
  .finally(() => {
    if (btn) {
      btn.disabled = false;
      btn.innerHTML = '<i class="fas fa-unlink me-1"></i>' + Drupal.t('Déconnecter');
    }
  });
}

})(jQuery, Drupal, once);
