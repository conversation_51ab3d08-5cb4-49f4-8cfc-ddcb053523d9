<?php

/**
 * Script de test pour vérifier le dashboard client
 */

// Vérifier que le template existe
$template_path = __DIR__ . '/templates/client-dashboard.html.twig';
echo "Template client-dashboard.html.twig existe : " . (file_exists($template_path) ? "OUI" : "NON") . "\n";

if (file_exists($template_path)) {
    $content = file_get_contents($template_path);
    
    // Vérifier la présence du bouton "Clôturer avec code"
    $has_button = strpos($content, 'btn-cloture-avec-code') !== false;
    echo "Bouton 'Clôturer avec code' présent : " . ($has_button ? "OUI" : "NON") . "\n";
    
    // Vérifier la présence de la modal
    $has_modal = strpos($content, 'modalClotureAvecCode') !== false;
    echo "Modal de clôture avec code présente : " . ($has_modal ? "OUI" : "NON") . "\n";
    
    // Compter les lignes
    $lines = substr_count($content, "\n");
    echo "Nombre de lignes dans le template : " . $lines . "\n";
}

// Vérifier que le JavaScript existe
$js_path = __DIR__ . '/js/dashboard.js';
echo "JavaScript dashboard.js existe : " . (file_exists($js_path) ? "OUI" : "NON") . "\n";

if (file_exists($js_path)) {
    $js_content = file_get_contents($js_path);
    
    // Vérifier la présence du code pour le bouton
    $has_js_button = strpos($js_content, 'btnConfirmClotureAvecCode') !== false;
    echo "JavaScript pour bouton 'Clôturer avec code' présent : " . ($has_js_button ? "OUI" : "NON") . "\n";
    
    // Vérifier l'URL de l'endpoint
    $has_endpoint = strpos($js_content, '/cloturer-avec-code') !== false;
    echo "Endpoint '/cloturer-avec-code' dans JavaScript : " . ($has_endpoint ? "OUI" : "NON") . "\n";
}

// Vérifier que le contrôleur a la méthode
$controller_path = __DIR__ . '/src/Controller/ClientController.php';
echo "Contrôleur ClientController.php existe : " . (file_exists($controller_path) ? "OUI" : "NON") . "\n";

if (file_exists($controller_path)) {
    $controller_content = file_get_contents($controller_path);
    
    // Vérifier la présence de la méthode
    $has_method = strpos($controller_content, 'clotureProjetAvecCode') !== false;
    echo "Méthode 'clotureProjetAvecCode' présente : " . ($has_method ? "OUI" : "NON") . "\n";
}

// Vérifier que la route existe
$routing_path = __DIR__ . '/client_demandes.routing.yml';
echo "Fichier de routes existe : " . (file_exists($routing_path) ? "OUI" : "NON") . "\n";

if (file_exists($routing_path)) {
    $routing_content = file_get_contents($routing_path);
    
    // Vérifier la présence de la route
    $has_route = strpos($routing_content, 'cloture_projet_avec_code') !== false;
    echo "Route 'cloture_projet_avec_code' présente : " . ($has_route ? "OUI" : "NON") . "\n";
}

echo "\n=== RÉSUMÉ ===\n";
echo "Tous les fichiers nécessaires sont en place.\n";
echo "Pour voir le bouton 'Clôturer avec code', vous devez :\n";
echo "1. Être connecté en tant que client\n";
echo "2. Avoir des devis avec le statut 'paid', 'depot' ou 'completed'\n";
echo "3. Aller sur /client/dashboard\n";
